//
//  ErrorHandlingService.swift
//  VirtualLocationApp
//
//  Created by Developer on 2025-06-20.
//  Copyright © 2025 YourCompany. All rights reserved.
//

import Foundation
import UIKit

// MARK: - Error Handling Service

class ErrorHandlingService {
    
    // MARK: - Singleton
    
    static let shared = ErrorHandlingService()
    
    // MARK: - Properties
    
    private var errorLog: [ErrorLogEntry] = []
    private let maxLogEntries = 100
    private var isLoggingEnabled = true
    
    // MARK: - Initialization
    
    private init() {
        setupErrorHandling()
    }
    
    // MARK: - Public Methods
    
    /// 处理错误
    func handleError(_ error: Error, context: String? = nil, showToUser: Bool = true) {
        // 记录错误
        logError(error, context: context)
        
        // 根据错误类型决定处理方式
        let errorInfo = analyzeError(error)
        
        // 显示给用户（如果需要）
        if showToUser {
            showErrorToUser(errorInfo)
        }
        
        // 尝试自动恢复
        attemptAutoRecovery(for: errorInfo)
        
        print("❌ 错误处理: \(errorInfo.description)")
    }
    
    /// 处理位置服务错误
    func handleLocationError(_ error: LocationError, showToUser: Bool = true) {
        let errorInfo = ErrorInfo(
            type: .locationService,
            originalError: error,
            description: error.localizedDescription,
            severity: determineSeverity(for: error),
            recoveryOptions: getRecoveryOptions(for: error)
        )
        
        logError(error, context: "LocationService")
        
        if showToUser {
            showLocationErrorToUser(errorInfo)
        }
        
        attemptLocationErrorRecovery(for: error)
    }
    
    /// 处理路径模拟错误
    func handlePathSimulationError(_ error: PathSimulationError, showToUser: Bool = true) {
        let errorInfo = ErrorInfo(
            type: .pathSimulation,
            originalError: error,
            description: error.localizedDescription,
            severity: .medium,
            recoveryOptions: ["重试路径模拟", "选择其他路径", "切换到单点定位"]
        )
        
        logError(error, context: "PathSimulation")
        
        if showToUser {
            showErrorToUser(errorInfo)
        }
    }
    
    /// 获取错误日志
    func getErrorLog() -> [ErrorLogEntry] {
        return errorLog
    }
    
    /// 清空错误日志
    func clearErrorLog() {
        errorLog.removeAll()
    }
    
    /// 导出错误日志
    func exportErrorLog() -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .medium
        
        var logString = "虚拟定位应用错误日志\n"
        logString += "导出时间: \(formatter.string(from: Date()))\n\n"
        
        for entry in errorLog {
            logString += "时间: \(formatter.string(from: entry.timestamp))\n"
            logString += "类型: \(entry.errorType)\n"
            logString += "描述: \(entry.description)\n"
            logString += "上下文: \(entry.context ?? "无")\n"
            logString += "严重程度: \(entry.severity)\n"
            logString += "---\n\n"
        }
        
        return logString
    }
    
    // MARK: - Private Methods
    
    private func setupErrorHandling() {
        // 设置全局异常处理
        NSSetUncaughtExceptionHandler { exception in
            ErrorHandlingService.shared.handleCriticalError(exception)
        }
    }
    
    private func logError(_ error: Error, context: String?) {
        guard isLoggingEnabled else { return }
        
        let entry = ErrorLogEntry(
            error: error,
            context: context,
            timestamp: Date()
        )
        
        errorLog.append(entry)
        
        // 限制日志条目数量
        if errorLog.count > maxLogEntries {
            errorLog.removeFirst()
        }
    }
    
    private func analyzeError(_ error: Error) -> ErrorInfo {
        let errorType: ErrorType
        let severity: ErrorSeverity
        let recoveryOptions: [String]
        
        switch error {
        case is LocationError:
            errorType = .locationService
            severity = .high
            recoveryOptions = ["检查位置权限", "重启位置服务", "重启应用"]
            
        case is PathSimulationError:
            errorType = .pathSimulation
            severity = .medium
            recoveryOptions = ["重试路径模拟", "选择其他路径"]
            
        case is DecodingError, is EncodingError:
            errorType = .dataCorruption
            severity = .medium
            recoveryOptions = ["重置应用数据", "重启应用"]
            
        default:
            errorType = .unknown
            severity = .low
            recoveryOptions = ["重试操作", "重启应用"]
        }
        
        return ErrorInfo(
            type: errorType,
            originalError: error,
            description: error.localizedDescription,
            severity: severity,
            recoveryOptions: recoveryOptions
        )
    }
    
    private func determineSeverity(for error: LocationError) -> ErrorSeverity {
        switch error {
        case .locationServicesDisabled, .permissionDenied:
            return .high
        case .invalidCoordinate, .invalidPath:
            return .medium
        case .simulationFailed, .pathSimulationFailed:
            return .low
        }
    }
    
    private func getRecoveryOptions(for error: LocationError) -> [String] {
        switch error {
        case .locationServicesDisabled:
            return ["开启位置服务", "前往设置"]
        case .permissionDenied:
            return ["授予位置权限", "前往设置"]
        case .invalidCoordinate:
            return ["重新选择位置", "输入有效坐标"]
        case .invalidPath:
            return ["重新绘制路径", "选择预设路径"]
        case .simulationFailed, .pathSimulationFailed:
            return ["重试", "重启应用"]
        }
    }
    
    private func showErrorToUser(_ errorInfo: ErrorInfo) {
        DispatchQueue.main.async {
            guard let topViewController = UIApplication.shared.topViewController else { return }
            
            let alert = UIAlertController(
                title: "错误",
                message: errorInfo.description,
                preferredStyle: .alert
            )
            
            // 添加恢复选项
            for option in errorInfo.recoveryOptions.prefix(2) { // 最多显示2个选项
                alert.addAction(UIAlertAction(title: option, style: .default) { _ in
                    self.executeRecoveryOption(option, for: errorInfo)
                })
            }
            
            alert.addAction(UIAlertAction(title: "确定", style: .cancel))
            
            topViewController.present(alert, animated: true)
        }
    }
    
    private func showLocationErrorToUser(_ errorInfo: ErrorInfo) {
        DispatchQueue.main.async {
            guard let topViewController = UIApplication.shared.topViewController else { return }
            
            let alert = UIAlertController(
                title: "位置服务错误",
                message: errorInfo.description,
                preferredStyle: .alert
            )
            
            if errorInfo.recoveryOptions.contains("前往设置") {
                alert.addAction(UIAlertAction(title: "前往设置", style: .default) { _ in
                    if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                        UIApplication.shared.open(settingsUrl)
                    }
                })
            }
            
            alert.addAction(UIAlertAction(title: "确定", style: .cancel))
            
            topViewController.present(alert, animated: true)
        }
    }
    
    private func attemptAutoRecovery(for errorInfo: ErrorInfo) {
        switch errorInfo.type {
        case .locationService:
            // 尝试重新初始化位置服务
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                LocationService.shared.requestLocationPermission()
            }
            
        case .pathSimulation:
            // 尝试重启路径模拟
            break
            
        case .dataCorruption:
            // 尝试恢复数据
            break
            
        case .network:
            // 尝试重新连接
            break
            
        case .unknown:
            break
        }
    }
    
    private func attemptLocationErrorRecovery(for error: LocationError) {
        switch error {
        case .simulationFailed, .pathSimulationFailed:
            // 尝试重启虚拟定位
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                // 可以在这里实现自动重试逻辑
            }
        default:
            break
        }
    }
    
    private func executeRecoveryOption(_ option: String, for errorInfo: ErrorInfo) {
        switch option {
        case "前往设置":
            if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsUrl)
            }
            
        case "重试":
            // 实现重试逻辑
            break
            
        case "重启应用":
            // 显示重启提示
            showRestartAppAlert()
            
        default:
            break
        }
    }
    
    private func showRestartAppAlert() {
        DispatchQueue.main.async {
            guard let topViewController = UIApplication.shared.topViewController else { return }
            
            let alert = UIAlertController(
                title: "重启应用",
                message: "请手动关闭并重新打开应用以解决问题。",
                preferredStyle: .alert
            )
            
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            
            topViewController.present(alert, animated: true)
        }
    }
    
    private func handleCriticalError(_ exception: NSException) {
        let errorEntry = ErrorLogEntry(
            errorType: "CriticalException",
            description: exception.description,
            context: "Application",
            severity: .critical,
            timestamp: Date()
        )
        
        errorLog.append(errorEntry)
        
        // 保存错误日志到文件
        saveErrorLogToFile()
    }
    
    private func saveErrorLogToFile() {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let logFileURL = documentsPath.appendingPathComponent("error_log.txt")
        
        let logContent = exportErrorLog()
        
        try? logContent.write(to: logFileURL, atomically: true, encoding: .utf8)
    }
}

// MARK: - Error Models

struct ErrorInfo {
    let type: ErrorType
    let originalError: Error
    let description: String
    let severity: ErrorSeverity
    let recoveryOptions: [String]
}

struct ErrorLogEntry {
    let id = UUID()
    let errorType: String
    let description: String
    let context: String?
    let severity: ErrorSeverity
    let timestamp: Date
    
    init(error: Error, context: String?, timestamp: Date) {
        self.errorType = String(describing: type(of: error))
        self.description = error.localizedDescription
        self.context = context
        self.severity = .medium
        self.timestamp = timestamp
    }
    
    init(errorType: String, description: String, context: String?, severity: ErrorSeverity, timestamp: Date) {
        self.errorType = errorType
        self.description = description
        self.context = context
        self.severity = severity
        self.timestamp = timestamp
    }
}

enum ErrorType {
    case locationService
    case pathSimulation
    case dataCorruption
    case network
    case unknown
}

enum ErrorSeverity: String, CaseIterable {
    case low = "低"
    case medium = "中"
    case high = "高"
    case critical = "严重"
}
