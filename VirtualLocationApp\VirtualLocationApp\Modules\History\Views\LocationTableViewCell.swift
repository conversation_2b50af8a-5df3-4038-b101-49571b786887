//
//  LocationTableViewCell.swift
//  VirtualLocationApp
//
//  Created by <PERSON><PERSON><PERSON> on 2025-06-20.
//  Copyright © 2025 YourCompany. All rights reserved.
//

import UIKit
import CoreLocation

protocol LocationTableViewCellDelegate: AnyObject {
    func locationTableViewCell(_ cell: LocationTableViewCell, didTapFavoriteButton location: VirtualLocationModel)
}

class LocationTableViewCell: UITableViewCell {
    
    // MARK: - UI Elements
    
    private let nameLabel = UILabel()
    private let addressLabel = UILabel()
    private let coordinateLabel = UILabel()
    private let timestampLabel = UILabel()
    private let favoriteButton = UIButton(type: .system)
    private let accuracyLabel = UILabel()
    private let containerView = UIView()
    
    // MARK: - Properties
    
    weak var delegate: LocationTableViewCellDelegate?
    private var location: VirtualLocationModel?
    
    // MARK: - Initialization
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        selectionStyle = .none
        backgroundColor = .clear
        
        setupContainerView()
        setupLabels()
        setupFavoriteButton()
        setupLayout()
    }
    
    private func setupContainerView() {
        containerView.backgroundColor = .secondarySystemGroupedBackground
        containerView.layer.cornerRadius = 12
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 1)
        containerView.layer.shadowOpacity = 0.1
        containerView.layer.shadowRadius = 2
        
        contentView.addSubview(containerView)
    }
    
    private func setupLabels() {
        // 名称标签
        nameLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        nameLabel.textColor = .label
        nameLabel.numberOfLines = 1
        
        // 地址标签
        addressLabel.font = UIFont.systemFont(ofSize: 14)
        addressLabel.textColor = .secondaryLabel
        addressLabel.numberOfLines = 2
        
        // 坐标标签
        coordinateLabel.font = UIFont.monospacedSystemFont(ofSize: 12, weight: .regular)
        coordinateLabel.textColor = .tertiaryLabel
        coordinateLabel.numberOfLines = 1
        
        // 时间标签
        timestampLabel.font = UIFont.systemFont(ofSize: 12)
        timestampLabel.textColor = .tertiaryLabel
        timestampLabel.numberOfLines = 1
        
        // 精度标签
        accuracyLabel.font = UIFont.systemFont(ofSize: 11)
        accuracyLabel.textColor = .systemBlue
        accuracyLabel.backgroundColor = UIColor.systemBlue.withAlphaComponent(0.1)
        accuracyLabel.layer.cornerRadius = 8
        accuracyLabel.layer.masksToBounds = true
        accuracyLabel.textAlignment = .center
        
        [nameLabel, addressLabel, coordinateLabel, timestampLabel, accuracyLabel].forEach {
            containerView.addSubview($0)
        }
    }
    
    private func setupFavoriteButton() {
        favoriteButton.setImage(UIImage(systemName: "heart"), for: .normal)
        favoriteButton.setImage(UIImage(systemName: "heart.fill"), for: .selected)
        favoriteButton.tintColor = .systemRed
        favoriteButton.addTarget(self, action: #selector(favoriteButtonTapped), for: .touchUpInside)
        
        containerView.addSubview(favoriteButton)
    }
    
    private func setupLayout() {
        containerView.translatesAutoresizingMaskIntoConstraints = false
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        addressLabel.translatesAutoresizingMaskIntoConstraints = false
        coordinateLabel.translatesAutoresizingMaskIntoConstraints = false
        timestampLabel.translatesAutoresizingMaskIntoConstraints = false
        favoriteButton.translatesAutoresizingMaskIntoConstraints = false
        accuracyLabel.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            // Container View
            containerView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 16),
            containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16),
            containerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -8),
            
            // Favorite Button
            favoriteButton.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 12),
            favoriteButton.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -12),
            favoriteButton.widthAnchor.constraint(equalToConstant: 24),
            favoriteButton.heightAnchor.constraint(equalToConstant: 24),
            
            // Name Label
            nameLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 12),
            nameLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            nameLabel.trailingAnchor.constraint(equalTo: favoriteButton.leadingAnchor, constant: -8),
            
            // Address Label
            addressLabel.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 4),
            addressLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            addressLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),
            
            // Coordinate Label
            coordinateLabel.topAnchor.constraint(equalTo: addressLabel.bottomAnchor, constant: 8),
            coordinateLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            coordinateLabel.trailingAnchor.constraint(lessThanOrEqualTo: accuracyLabel.leadingAnchor, constant: -8),
            
            // Accuracy Label
            accuracyLabel.centerYAnchor.constraint(equalTo: coordinateLabel.centerYAnchor),
            accuracyLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),
            accuracyLabel.widthAnchor.constraint(equalToConstant: 50),
            accuracyLabel.heightAnchor.constraint(equalToConstant: 20),
            
            // Timestamp Label
            timestampLabel.topAnchor.constraint(equalTo: coordinateLabel.bottomAnchor, constant: 8),
            timestampLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            timestampLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),
            timestampLabel.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -12)
        ])
    }
    
    // MARK: - Configuration
    
    func configure(with location: VirtualLocationModel, isFavorited: Bool, showFavoriteButton: Bool) {
        self.location = location
        
        nameLabel.text = location.name
        addressLabel.text = location.address ?? "未知地址"
        coordinateLabel.text = String(format: "%.6f, %.6f", location.latitude, location.longitude)
        accuracyLabel.text = String(format: "%.0fm", location.accuracy)
        
        // 格式化时间
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        timestampLabel.text = formatter.string(from: location.timestamp)
        
        // 设置收藏按钮
        favoriteButton.isSelected = isFavorited
        favoriteButton.isHidden = !showFavoriteButton
        
        // 如果地址为空，调整布局
        if location.address?.isEmpty != false {
            addressLabel.text = "点击查看详细位置信息"
            addressLabel.textColor = .tertiaryLabel
        } else {
            addressLabel.textColor = .secondaryLabel
        }
    }
    
    // MARK: - Actions
    
    @objc private func favoriteButtonTapped() {
        guard let location = location else { return }
        
        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
        
        // 添加动画效果
        UIView.animate(withDuration: 0.1, animations: {
            self.favoriteButton.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.favoriteButton.transform = .identity
            }
        }
        
        delegate?.locationTableViewCell(self, didTapFavoriteButton: location)
    }
    
    // MARK: - Reuse
    
    override func prepareForReuse() {
        super.prepareForReuse()
        nameLabel.text = nil
        addressLabel.text = nil
        coordinateLabel.text = nil
        timestampLabel.text = nil
        accuracyLabel.text = nil
        favoriteButton.isSelected = false
        favoriteButton.isHidden = false
        location = nil
    }
}

// MARK: - EmptyStateView

class EmptyStateView: UIView {
    
    private let imageView = UIImageView()
    private let titleLabel = UILabel()
    private let messageLabel = UILabel()
    private let stackView = UIStackView()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    private func setupUI() {
        // 图片视图
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = .tertiaryLabel
        
        // 标题标签
        titleLabel.font = UIFont.systemFont(ofSize: 20, weight: .semibold)
        titleLabel.textColor = .secondaryLabel
        titleLabel.textAlignment = .center
        titleLabel.numberOfLines = 0
        
        // 消息标签
        messageLabel.font = UIFont.systemFont(ofSize: 16)
        messageLabel.textColor = .tertiaryLabel
        messageLabel.textAlignment = .center
        messageLabel.numberOfLines = 0
        
        // 堆栈视图
        stackView.axis = .vertical
        stackView.alignment = .center
        stackView.spacing = 16
        stackView.translatesAutoresizingMaskIntoConstraints = false
        
        stackView.addArrangedSubview(imageView)
        stackView.addArrangedSubview(titleLabel)
        stackView.addArrangedSubview(messageLabel)
        
        addSubview(stackView)
        
        NSLayoutConstraint.activate([
            imageView.widthAnchor.constraint(equalToConstant: 80),
            imageView.heightAnchor.constraint(equalToConstant: 80),
            
            stackView.topAnchor.constraint(equalTo: topAnchor),
            stackView.leadingAnchor.constraint(equalTo: leadingAnchor),
            stackView.trailingAnchor.constraint(equalTo: trailingAnchor),
            stackView.bottomAnchor.constraint(equalTo: bottomAnchor)
        ])
    }
    
    func configure(image: UIImage?, title: String, message: String) {
        imageView.image = image
        titleLabel.text = title
        messageLabel.text = message
    }
}
