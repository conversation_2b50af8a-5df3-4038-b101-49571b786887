//
//  SettingsViewController.swift
//  VirtualLocationApp
//
//  Created by Developer on 2025-06-20.
//  Copyright © 2025 YourCompany. All rights reserved.
//

import UIKit

class SettingsViewController: UITableViewController {
    
    // MARK: - Properties
    
    private let dataService = DataService.shared
    private var appConfiguration: AppConfiguration
    
    // MARK: - Section and Row Definitions
    
    private enum Section: Int, CaseIterable {
        case location = 0
        case map
        case general
        case data
        case about
        
        var title: String {
            switch self {
            case .location:
                return "位置设置"
            case .map:
                return "地图设置"
            case .general:
                return "通用设置"
            case .data:
                return "数据管理"
            case .about:
                return "关于"
            }
        }
    }
    
    private enum LocationRow: Int, CaseIterable {
        case defaultAccuracy = 0
        case updateInterval
        case autoStart
        
        var title: String {
            switch self {
            case .defaultAccuracy:
                return "默认精度"
            case .updateInterval:
                return "更新频率"
            case .autoStart:
                return "自动启动"
            }
        }
    }
    
    private enum MapRow: Int, CaseIterable {
        case mapType = 0
        case showUserLocation
        
        var title: String {
            switch self {
            case .mapType:
                return "地图类型"
            case .showUserLocation:
                return "显示用户位置"
            }
        }
    }
    
    private enum GeneralRow: Int, CaseIterable {
        case hapticFeedback = 0
        case maxHistoryCount
        
        var title: String {
            switch self {
            case .hapticFeedback:
                return "触觉反馈"
            case .maxHistoryCount:
                return "历史记录上限"
            }
        }
    }
    
    private enum DataRow: Int, CaseIterable {
        case clearHistory = 0
        case exportData
        case importData
        case clearAllData
        
        var title: String {
            switch self {
            case .clearHistory:
                return "清空历史记录"
            case .exportData:
                return "导出数据"
            case .importData:
                return "导入数据"
            case .clearAllData:
                return "清空所有数据"
            }
        }
    }
    
    private enum AboutRow: Int, CaseIterable {
        case version = 0
        case privacy
        case terms
        case contact
        
        var title: String {
            switch self {
            case .version:
                return "版本信息"
            case .privacy:
                return "隐私政策"
            case .terms:
                return "使用条款"
            case .contact:
                return "联系我们"
            }
        }
    }
    
    // MARK: - Lifecycle
    
    init() {
        self.appConfiguration = DataService.shared.getAppConfiguration()
        super.init(style: .insetGrouped)
    }
    
    required init?(coder: NSCoder) {
        self.appConfiguration = DataService.shared.getAppConfiguration()
        super.init(coder: coder)
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupTableView()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        saveConfiguration()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        title = "设置"
        navigationItem.largeTitleDisplayMode = .never
        
        // 添加完成按钮
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .done,
            target: self,
            action: #selector(doneButtonTapped)
        )
    }
    
    private func setupTableView() {
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "Cell")
        tableView.register(SwitchTableViewCell.self, forCellReuseIdentifier: "SwitchCell")
        tableView.register(SliderTableViewCell.self, forCellReuseIdentifier: "SliderCell")
        tableView.register(DetailTableViewCell.self, forCellReuseIdentifier: "DetailCell")
    }
    
    // MARK: - Actions
    
    @objc private func doneButtonTapped() {
        saveConfiguration()
        dismiss(animated: true)
    }
    
    private func saveConfiguration() {
        dataService.saveAppConfiguration(appConfiguration)
    }
    
    // MARK: - TableView DataSource
    
    override func numberOfSections(in tableView: UITableView) -> Int {
        return Section.allCases.count
    }
    
    override func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard let sectionType = Section(rawValue: section) else { return 0 }
        
        switch sectionType {
        case .location:
            return LocationRow.allCases.count
        case .map:
            return MapRow.allCases.count
        case .general:
            return GeneralRow.allCases.count
        case .data:
            return DataRow.allCases.count
        case .about:
            return AboutRow.allCases.count
        }
    }
    
    override func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        return Section(rawValue: section)?.title
    }
    
    override func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let sectionType = Section(rawValue: indexPath.section) else {
            return UITableViewCell()
        }
        
        switch sectionType {
        case .location:
            return configureLocationCell(at: indexPath)
        case .map:
            return configureMapCell(at: indexPath)
        case .general:
            return configureGeneralCell(at: indexPath)
        case .data:
            return configureDataCell(at: indexPath)
        case .about:
            return configureAboutCell(at: indexPath)
        }
    }
    
    // MARK: - Cell Configuration
    
    private func configureLocationCell(at indexPath: IndexPath) -> UITableViewCell {
        guard let rowType = LocationRow(rawValue: indexPath.row) else {
            return UITableViewCell()
        }
        
        switch rowType {
        case .defaultAccuracy:
            let cell = tableView.dequeueReusableCell(withIdentifier: "SliderCell", for: indexPath) as! SliderTableViewCell
            cell.configure(
                title: rowType.title,
                value: Float(appConfiguration.defaultAccuracy),
                minimumValue: 1.0,
                maximumValue: 100.0,
                unit: "m"
            ) { [weak self] value in
                self?.appConfiguration.defaultAccuracy = Double(value)
            }
            return cell
            
        case .updateInterval:
            let cell = tableView.dequeueReusableCell(withIdentifier: "SliderCell", for: indexPath) as! SliderTableViewCell
            cell.configure(
                title: rowType.title,
                value: Float(appConfiguration.updateInterval),
                minimumValue: 0.5,
                maximumValue: 5.0,
                unit: "s"
            ) { [weak self] value in
                self?.appConfiguration.updateInterval = Double(value)
            }
            return cell
            
        case .autoStart:
            let cell = tableView.dequeueReusableCell(withIdentifier: "SwitchCell", for: indexPath) as! SwitchTableViewCell
            cell.configure(
                title: rowType.title,
                isOn: appConfiguration.isAutoStartEnabled
            ) { [weak self] isOn in
                self?.appConfiguration.isAutoStartEnabled = isOn
            }
            return cell
        }
    }
    
    private func configureMapCell(at indexPath: IndexPath) -> UITableViewCell {
        guard let rowType = MapRow(rawValue: indexPath.row) else {
            return UITableViewCell()
        }
        
        switch rowType {
        case .mapType:
            let cell = tableView.dequeueReusableCell(withIdentifier: "DetailCell", for: indexPath) as! DetailTableViewCell
            let mapTypes = ["标准", "卫星", "混合"]
            cell.configure(
                title: rowType.title,
                detail: mapTypes[appConfiguration.mapType]
            )
            cell.accessoryType = .disclosureIndicator
            return cell
            
        case .showUserLocation:
            let cell = tableView.dequeueReusableCell(withIdentifier: "SwitchCell", for: indexPath) as! SwitchTableViewCell
            cell.configure(
                title: rowType.title,
                isOn: appConfiguration.showsUserLocation
            ) { [weak self] isOn in
                self?.appConfiguration.showsUserLocation = isOn
            }
            return cell
        }
    }
    
    private func configureGeneralCell(at indexPath: IndexPath) -> UITableViewCell {
        guard let rowType = GeneralRow(rawValue: indexPath.row) else {
            return UITableViewCell()
        }
        
        switch rowType {
        case .hapticFeedback:
            let cell = tableView.dequeueReusableCell(withIdentifier: "SwitchCell", for: indexPath) as! SwitchTableViewCell
            cell.configure(
                title: rowType.title,
                isOn: appConfiguration.enableHapticFeedback
            ) { [weak self] isOn in
                self?.appConfiguration.enableHapticFeedback = isOn
            }
            return cell
            
        case .maxHistoryCount:
            let cell = tableView.dequeueReusableCell(withIdentifier: "DetailCell", for: indexPath) as! DetailTableViewCell
            cell.configure(
                title: rowType.title,
                detail: "\(appConfiguration.maxHistoryCount)"
            )
            cell.accessoryType = .disclosureIndicator
            return cell
        }
    }
    
    private func configureDataCell(at indexPath: IndexPath) -> UITableViewCell {
        guard let rowType = DataRow(rawValue: indexPath.row) else {
            return UITableViewCell()
        }
        
        let cell = tableView.dequeueReusableCell(withIdentifier: "Cell", for: indexPath)
        cell.textLabel?.text = rowType.title
        cell.accessoryType = .disclosureIndicator
        
        switch rowType {
        case .clearHistory, .clearAllData:
            cell.textLabel?.textColor = .systemRed
        default:
            cell.textLabel?.textColor = .label
        }
        
        return cell
    }
    
    private func configureAboutCell(at indexPath: IndexPath) -> UITableViewCell {
        guard let rowType = AboutRow(rawValue: indexPath.row) else {
            return UITableViewCell()
        }
        
        let cell = tableView.dequeueReusableCell(withIdentifier: "DetailCell", for: indexPath) as! DetailTableViewCell
        
        switch rowType {
        case .version:
            let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
            let build = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
            cell.configure(title: rowType.title, detail: "\(version) (\(build))")
        case .privacy, .terms, .contact:
            cell.configure(title: rowType.title, detail: "")
            cell.accessoryType = .disclosureIndicator
        }
        
        return cell
    }

    // MARK: - TableView Delegate

    override func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)

        guard let sectionType = Section(rawValue: indexPath.section) else { return }

        switch sectionType {
        case .map:
            handleMapRowSelection(at: indexPath)
        case .general:
            handleGeneralRowSelection(at: indexPath)
        case .data:
            handleDataRowSelection(at: indexPath)
        case .about:
            handleAboutRowSelection(at: indexPath)
        default:
            break
        }
    }

    private func handleMapRowSelection(at indexPath: IndexPath) {
        guard let rowType = MapRow(rawValue: indexPath.row) else { return }

        switch rowType {
        case .mapType:
            showMapTypeSelector()
        default:
            break
        }
    }

    private func handleGeneralRowSelection(at indexPath: IndexPath) {
        guard let rowType = GeneralRow(rawValue: indexPath.row) else { return }

        switch rowType {
        case .maxHistoryCount:
            showHistoryCountSelector()
        default:
            break
        }
    }

    private func handleDataRowSelection(at indexPath: IndexPath) {
        guard let rowType = DataRow(rawValue: indexPath.row) else { return }

        switch rowType {
        case .clearHistory:
            showClearHistoryConfirmation()
        case .exportData:
            exportData()
        case .importData:
            importData()
        case .clearAllData:
            showClearAllDataConfirmation()
        }
    }

    private func handleAboutRowSelection(at indexPath: IndexPath) {
        guard let rowType = AboutRow(rawValue: indexPath.row) else { return }

        switch rowType {
        case .privacy:
            showPrivacyPolicy()
        case .terms:
            showTermsOfService()
        case .contact:
            showContactInfo()
        default:
            break
        }
    }

    // MARK: - Action Methods

    private func showMapTypeSelector() {
        let alert = UIAlertController(title: "地图类型", message: "选择地图显示类型", preferredStyle: .actionSheet)

        let mapTypes = ["标准", "卫星", "混合"]
        for (index, type) in mapTypes.enumerated() {
            let action = UIAlertAction(title: type, style: .default) { [weak self] _ in
                self?.appConfiguration.mapType = index
                self?.tableView.reloadRows(at: [IndexPath(row: MapRow.mapType.rawValue, section: Section.map.rawValue)], with: .none)
            }
            if index == appConfiguration.mapType {
                action.setValue(true, forKey: "checked")
            }
            alert.addAction(action)
        }

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))

        if let popover = alert.popoverPresentationController {
            popover.sourceView = tableView
            popover.sourceRect = tableView.rectForRow(at: IndexPath(row: MapRow.mapType.rawValue, section: Section.map.rawValue))
        }

        present(alert, animated: true)
    }

    private func showHistoryCountSelector() {
        let alert = UIAlertController(title: "历史记录上限", message: "设置保存的历史记录数量", preferredStyle: .alert)

        alert.addTextField { textField in
            textField.placeholder = "输入数量 (10-200)"
            textField.text = "\(self.appConfiguration.maxHistoryCount)"
            textField.keyboardType = .numberPad
        }

        let confirmAction = UIAlertAction(title: "确定", style: .default) { [weak self] _ in
            guard let textField = alert.textFields?.first,
                  let text = textField.text,
                  let count = Int(text),
                  count >= 10 && count <= 200 else {
                self?.showAlert(title: "错误", message: "请输入10-200之间的数字")
                return
            }

            self?.appConfiguration.maxHistoryCount = count
            self?.tableView.reloadRows(at: [IndexPath(row: GeneralRow.maxHistoryCount.rawValue, section: Section.general.rawValue)], with: .none)
        }

        alert.addAction(confirmAction)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))

        present(alert, animated: true)
    }

    private func showClearHistoryConfirmation() {
        let alert = UIAlertController(
            title: "清空历史记录",
            message: "此操作将删除所有位置历史记录，无法恢复。确定要继续吗？",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "确定", style: .destructive) { [weak self] _ in
            self?.dataService.clearLocationHistory()
            self?.showAlert(title: "成功", message: "历史记录已清空")
        })

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))

        present(alert, animated: true)
    }

    private func showClearAllDataConfirmation() {
        let alert = UIAlertController(
            title: "清空所有数据",
            message: "此操作将删除所有数据，包括历史记录、收藏位置和设置，无法恢复。确定要继续吗？",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "确定", style: .destructive) { [weak self] _ in
            self?.dataService.clearAllData()
            self?.appConfiguration = DataService.shared.getAppConfiguration()
            self?.tableView.reloadData()
            self?.showAlert(title: "成功", message: "所有数据已清空")
        })

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))

        present(alert, animated: true)
    }

    private func exportData() {
        let data = dataService.exportData()

        do {
            let jsonData = try JSONSerialization.data(withJSONObject: data, options: .prettyPrinted)
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
            let fileURL = documentsPath.appendingPathComponent("VirtualLocationData.json")

            try jsonData.write(to: fileURL)

            let activityVC = UIActivityViewController(activityItems: [fileURL], applicationActivities: nil)

            if let popover = activityVC.popoverPresentationController {
                popover.sourceView = tableView
                popover.sourceRect = tableView.rectForRow(at: IndexPath(row: DataRow.exportData.rawValue, section: Section.data.rawValue))
            }

            present(activityVC, animated: true)

        } catch {
            showAlert(title: "导出失败", message: "无法导出数据: \(error.localizedDescription)")
        }
    }

    private func importData() {
        let documentPicker = UIDocumentPickerViewController(forOpeningContentTypes: [.json])
        documentPicker.delegate = self
        documentPicker.allowsMultipleSelection = false
        present(documentPicker, animated: true)
    }

    private func showPrivacyPolicy() {
        let alert = UIAlertController(
            title: "隐私政策",
            message: "我们重视您的隐私。此应用仅在本地处理位置数据，不会上传到服务器或与第三方分享。",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func showTermsOfService() {
        let alert = UIAlertController(
            title: "使用条款",
            message: "请合法合规使用本应用。不得用于欺骗商业服务或进行非法活动。用户需自行承担使用风险。",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func showContactInfo() {
        let alert = UIAlertController(
            title: "联系我们",
            message: "如有问题或建议，请联系：\n邮箱：<EMAIL>",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - UIDocumentPickerDelegate

extension SettingsViewController: UIDocumentPickerDelegate {

    func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
        guard let url = urls.first else { return }

        do {
            let data = try Data(contentsOf: url)
            let jsonObject = try JSONSerialization.jsonObject(with: data, options: [])

            // 这里可以实现数据导入逻辑
            showAlert(title: "导入成功", message: "数据已成功导入")

        } catch {
            showAlert(title: "导入失败", message: "无法读取文件: \(error.localizedDescription)")
        }
    }
}
