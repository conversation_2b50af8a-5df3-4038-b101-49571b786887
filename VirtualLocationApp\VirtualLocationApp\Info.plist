<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>虚拟定位助手</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0.0</string>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>应用需要访问您的位置信息以在地图上显示当前位置作为参考。您的位置信息仅用于应用功能，不会被上传或共享。</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>应用需要访问您的位置信息以提供虚拟定位服务。您的位置信息仅用于应用功能，不会被上传或共享。</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>Default Configuration</string>
					<key>UISceneDelegateClassName</key>
					<string>$(PRODUCT_MODULE_NAME).SceneDelegate</string>
					<key>UISceneStoryboardFile</key>
					<string>Main</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
		<string>location-services</string>
		<string>gps</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	
	<!-- 位置权限配置 -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>此应用需要访问您的当前位置以提供位置模拟服务。我们不会收集、存储或分享您的真实位置信息。</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>为了提供准确的虚拟定位服务，此应用需要持续访问位置权限。您的真实位置信息仅在设备本地使用，不会上传到服务器。</string>
	
	<!-- 应用传输安全设置 -->
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	
	<!-- 应用类别 -->
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.developer-tools</string>
	
	<!-- 支持的文档类型 -->
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>Virtual Location Data</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.yourcompany.virtuallocation.data</string>
			</array>
		</dict>
	</array>
	
	<!-- 导出的类型标识符 -->
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.json</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Virtual Location Data</string>
			<key>UTTypeIdentifier</key>
			<string>com.yourcompany.virtuallocation.data</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>vldata</string>
				</array>
				<key>public.mime-type</key>
				<array>
					<string>application/json</string>
				</array>
			</dict>
		</dict>
	</array>
	
	<!-- 后台模式 -->
	<key>UIBackgroundModes</key>
	<array>
		<string>location</string>
	</array>
	
	<!-- 状态栏样式 -->
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleLightContent</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	
	<!-- 启动图片配置 -->
	<key>UILaunchScreen</key>
	<dict>
		<key>UIImageName</key>
		<string>LaunchImage</string>
		<key>UIColorName</key>
		<string>LaunchBackgroundColor</string>
	</dict>
	
	<!-- 应用图标配置 -->
	<key>CFBundleIcons</key>
	<dict>
		<key>CFBundlePrimaryIcon</key>
		<dict>
			<key>CFBundleIconFiles</key>
			<array>
				<string>AppIcon20x20</string>
				<string>AppIcon29x29</string>
				<string>AppIcon40x40</string>
				<string>AppIcon60x60</string>
			</array>
			<key>CFBundleIconName</key>
			<string>AppIcon</string>
		</dict>
	</dict>
	
	<!-- 隐私配置 -->
	<key>NSPrivacyAccessedAPITypes</key>
	<array>
		<dict>
			<key>NSPrivacyAccessedAPIType</key>
			<string>NSPrivacyAccessedAPICategoryLocation</string>
			<key>NSPrivacyAccessedAPITypeReasons</key>
			<array>
				<string>CA92.1</string>
			</array>
		</dict>
	</array>
	
	<!-- 应用元数据 -->
	<key>NSHumanReadableCopyright</key>
	<string>Copyright © 2025 YourCompany. All rights reserved.</string>
	
	<!-- 最低系统版本 -->
	<key>MinimumOSVersion</key>
	<string>15.5</string>
	
	<!-- 设备兼容性 -->
	<key>UIDeviceFamily</key>
	<array>
		<integer>1</integer>
		<integer>2</integer>
	</array>
	
	<!-- 界面样式 -->
	<key>UIUserInterfaceStyle</key>
	<string>Automatic</string>
	
	<!-- 支持的界面方向 -->
	<key>UIInterfaceOrientationPortrait</key>
	<true/>
	<key>UIInterfaceOrientationLandscapeLeft</key>
	<true/>
	<key>UIInterfaceOrientationLandscapeRight</key>
	<true/>
	<key>UIInterfaceOrientationPortraitUpsideDown</key>
	<false/>
</dict>
</plist>
