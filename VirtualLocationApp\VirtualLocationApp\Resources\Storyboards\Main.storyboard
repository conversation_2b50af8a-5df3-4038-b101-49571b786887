<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="BYZ-38-t0r">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Map view" minToolsVersion="13.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Main View Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController id="BYZ-38-t0r" customClass="MainViewController" customModule="VirtualLocationApp" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <mapView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" mapType="standard" translatesAutoresizingMaskIntoConstraints="NO" id="map-view-id">
                                <rect key="frame" x="0.0" y="59" width="393" height="509"/>
                            </mapView>
                            <segmentedControl opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="top" segmentControlStyle="plain" selectedSegmentIndex="0" translatesAutoresizingMaskIntoConstraints="NO" id="map-type-control">
                                <rect key="frame" x="16" y="584" width="361" height="32"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="31" id="segment-height"/>
                                </constraints>
                                <segments>
                                    <segment title="标准"/>
                                    <segment title="卫星"/>
                                    <segment title="混合"/>
                                </segments>
                            </segmentedControl>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="search-button">
                                <rect key="frame" x="327" y="79" width="50" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="50" id="search-width"/>
                                    <constraint firstAttribute="height" constant="50" id="search-height"/>
                                </constraints>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="filled" image="magnifyingglass" catalog="system">
                                    <color key="baseBackgroundColor" systemColor="systemBlueColor"/>
                                </buttonConfiguration>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="current-location-button">
                                <rect key="frame" x="327" y="139" width="50" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="50" id="location-width"/>
                                    <constraint firstAttribute="height" constant="50" id="location-height"/>
                                </constraints>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="filled" image="location.fill" catalog="system">
                                    <color key="baseBackgroundColor" systemColor="systemBlueColor"/>
                                </buttonConfiguration>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="control-panel-view">
                                <rect key="frame" x="16" y="631" width="361" height="200"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="点击地图选择位置" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="coordinate-label">
                                        <rect key="frame" x="16" y="16" width="329" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="coordinate-height"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <color key="textColor" systemColor="secondaryLabelColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <slider opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" value="5" minValue="1" maxValue="100" translatesAutoresizingMaskIntoConstraints="NO" id="accuracy-slider">
                                        <rect key="frame" x="14" y="72" width="273" height="31"/>
                                    </slider>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="精度: 5m" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="accuracy-label">
                                        <rect key="frame" x="295" y="77" width="50" height="21"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="50" id="accuracy-width"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                        <color key="textColor" systemColor="secondaryLabelColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="start-button">
                                        <rect key="frame" x="16" y="118" width="150" height="36"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="36" id="start-height"/>
                                        </constraints>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="filled" title="开始虚拟定位">
                                            <color key="baseBackgroundColor" systemColor="systemGreenColor"/>
                                        </buttonConfiguration>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="stop-button">
                                        <rect key="frame" x="195" y="118" width="150" height="36"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="36" id="stop-height"/>
                                        </constraints>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="filled" title="停止虚拟定位">
                                            <color key="baseBackgroundColor" systemColor="systemRedColor"/>
                                        </buttonConfiguration>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="虚拟定位未激活" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="status-label">
                                        <rect key="frame" x="16" y="170" width="329" height="14"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                        <color key="textColor" systemColor="secondaryLabelColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="coordinate-label" firstAttribute="leading" secondItem="control-panel-view" secondAttribute="leading" constant="16" id="coordinate-leading"/>
                                    <constraint firstAttribute="trailing" secondItem="coordinate-label" secondAttribute="trailing" constant="16" id="coordinate-trailing"/>
                                    <constraint firstItem="coordinate-label" firstAttribute="top" secondItem="control-panel-view" secondAttribute="top" constant="16" id="coordinate-top"/>
                                    
                                    <constraint firstItem="accuracy-slider" firstAttribute="leading" secondItem="control-panel-view" secondAttribute="leading" constant="16" id="slider-leading"/>
                                    <constraint firstItem="accuracy-label" firstAttribute="leading" secondItem="accuracy-slider" secondAttribute="trailing" constant="8" id="slider-label-spacing"/>
                                    <constraint firstAttribute="trailing" secondItem="accuracy-label" secondAttribute="trailing" constant="16" id="label-trailing"/>
                                    <constraint firstItem="accuracy-slider" firstAttribute="centerY" secondItem="accuracy-label" secondAttribute="centerY" id="slider-center"/>
                                    <constraint firstItem="accuracy-slider" firstAttribute="top" secondItem="coordinate-label" secondAttribute="bottom" constant="16" id="slider-top"/>
                                    
                                    <constraint firstItem="start-button" firstAttribute="leading" secondItem="control-panel-view" secondAttribute="leading" constant="16" id="start-leading"/>
                                    <constraint firstItem="start-button" firstAttribute="top" secondItem="accuracy-slider" secondAttribute="bottom" constant="16" id="start-top"/>
                                    <constraint firstItem="start-button" firstAttribute="width" secondItem="stop-button" secondAttribute="width" id="buttons-equal-width"/>
                                    
                                    <constraint firstItem="stop-button" firstAttribute="leading" secondItem="start-button" secondAttribute="trailing" constant="29" id="buttons-spacing"/>
                                    <constraint firstAttribute="trailing" secondItem="stop-button" secondAttribute="trailing" constant="16" id="stop-trailing"/>
                                    <constraint firstItem="stop-button" firstAttribute="centerY" secondItem="start-button" secondAttribute="centerY" id="buttons-center"/>
                                    
                                    <constraint firstItem="status-label" firstAttribute="leading" secondItem="control-panel-view" secondAttribute="leading" constant="16" id="status-leading"/>
                                    <constraint firstAttribute="trailing" secondItem="status-label" secondAttribute="trailing" constant="16" id="status-trailing"/>
                                    <constraint firstItem="status-label" firstAttribute="top" secondItem="start-button" secondAttribute="bottom" constant="16" id="status-top"/>
                                    <constraint firstAttribute="bottom" secondItem="status-label" secondAttribute="bottom" constant="16" id="status-bottom"/>
                                    
                                    <constraint firstAttribute="height" constant="200" id="panel-height"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutMarginsGuide key="safeArea" id="6Tk-OE-BBY"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <!-- Map View Constraints -->
                            <constraint firstItem="map-view-id" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" id="map-leading"/>
                            <constraint firstItem="map-view-id" firstAttribute="trailing" secondItem="6Tk-OE-BBY" secondAttribute="trailing" id="map-trailing"/>
                            <constraint firstItem="map-view-id" firstAttribute="top" secondItem="6Tk-OE-BBY" secondAttribute="top" id="map-top"/>
                            <constraint firstItem="map-type-control" firstAttribute="top" secondItem="map-view-id" secondAttribute="bottom" constant="16" id="segment-map-spacing"/>

                            <!-- Map Type Control Constraints -->
                            <constraint firstItem="map-type-control" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="16" id="segment-leading"/>
                            <constraint firstItem="map-type-control" firstAttribute="trailing" secondItem="6Tk-OE-BBY" secondAttribute="trailing" constant="-16" id="segment-trailing"/>

                            <!-- Search Button Constraints -->
                            <constraint firstItem="search-button" firstAttribute="trailing" secondItem="6Tk-OE-BBY" secondAttribute="trailing" constant="-16" id="search-trailing"/>
                            <constraint firstItem="search-button" firstAttribute="top" secondItem="6Tk-OE-BBY" secondAttribute="top" constant="20" id="search-top"/>

                            <!-- Current Location Button Constraints -->
                            <constraint firstItem="current-location-button" firstAttribute="trailing" secondItem="6Tk-OE-BBY" secondAttribute="trailing" constant="-16" id="location-trailing"/>
                            <constraint firstItem="current-location-button" firstAttribute="top" secondItem="search-button" secondAttribute="bottom" constant="10" id="location-search-spacing"/>

                            <!-- Control Panel Constraints -->
                            <constraint firstItem="control-panel-view" firstAttribute="top" secondItem="map-type-control" secondAttribute="bottom" constant="16" id="panel-segment-spacing"/>
                            <constraint firstItem="control-panel-view" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="16" id="panel-leading"/>
                            <constraint firstItem="control-panel-view" firstAttribute="trailing" secondItem="6Tk-OE-BBY" secondAttribute="trailing" constant="-16" id="panel-trailing"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="bottom" secondItem="control-panel-view" secondAttribute="bottom" constant="18" id="panel-bottom"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="mapView" destination="map-view-id" id="map-outlet"/>
                        <outlet property="controlPanelView" destination="control-panel-view" id="panel-outlet"/>
                        <outlet property="startButton" destination="start-button" id="start-outlet"/>
                        <outlet property="stopButton" destination="stop-button" id="stop-outlet"/>
                        <outlet property="coordinateLabel" destination="coordinate-label" id="coordinate-outlet"/>
                        <outlet property="accuracySlider" destination="accuracy-slider" id="slider-outlet"/>
                        <outlet property="accuracyLabel" destination="accuracy-label" id="accuracy-outlet"/>
                        <outlet property="statusLabel" destination="status-label" id="status-outlet"/>
                        <outlet property="searchButton" destination="search-button" id="search-outlet"/>
                        <outlet property="currentLocationButton" destination="current-location-button" id="location-outlet"/>
                        <outlet property="mapTypeSegmentedControl" destination="map-type-control" id="segment-outlet"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzz" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="20" y="4"/>
        </scene>
    </scenes>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemGreenColor">
            <color red="0.20392156862745098" green="0.7803921568627451" blue="0.34901960784313724" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemRedColor">
            <color red="1" green="0.23137254901960785" blue="0.18823529411764706" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="secondaryLabelColor">
            <color red="0.23529411764705882" green="0.23529411764705882" blue="0.2627450980392157" alpha="0.59999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
