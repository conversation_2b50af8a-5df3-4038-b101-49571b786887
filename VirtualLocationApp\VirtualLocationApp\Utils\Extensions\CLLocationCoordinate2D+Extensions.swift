//
//  CLLocationCoordinate2D+Extensions.swift
//  VirtualLocationApp
//
//  Created by Developer on 2025-06-20.
//  Copyright © 2025 YourCompany. All rights reserved.
//

import Foundation
import CoreLocation

// MARK: - CLLocationCoordinate2D Extensions

extension CLLocationCoordinate2D {
    
    /// 检查坐标是否有效
    var isValid: Bool {
        return CLLocationCoordinate2DIsValid(self)
    }
    
    /// 格式化坐标为字符串
    var formattedString: String {
        return String(format: "%.6f, %.6f", latitude, longitude)
    }
    
    /// 详细格式化坐标
    var detailedString: String {
        return String(format: "纬度: %.6f\n经度: %.6f", latitude, longitude)
    }
    
    /// 计算与另一个坐标的距离（米）
    func distance(to coordinate: CLLocationCoordinate2D) -> CLLocationDistance {
        let location1 = CLLocation(latitude: self.latitude, longitude: self.longitude)
        let location2 = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
        return location1.distance(from: location2)
    }
    
    /// 计算中点坐标
    func midpoint(to coordinate: CLLocationCoordinate2D) -> CLLocationCoordinate2D {
        let lat = (self.latitude + coordinate.latitude) / 2.0
        let lon = (self.longitude + coordinate.longitude) / 2.0
        return CLLocationCoordinate2D(latitude: lat, longitude: lon)
    }
    
    /// 在指定方向和距离创建新坐标
    func coordinate(at bearing: Double, distance: CLLocationDistance) -> CLLocationCoordinate2D {
        let earthRadius = 6371000.0 // 地球半径（米）
        
        let lat1 = latitude * .pi / 180.0
        let lon1 = longitude * .pi / 180.0
        let bearingRad = bearing * .pi / 180.0
        
        let lat2 = asin(sin(lat1) * cos(distance / earthRadius) +
                       cos(lat1) * sin(distance / earthRadius) * cos(bearingRad))
        
        let lon2 = lon1 + atan2(sin(bearingRad) * sin(distance / earthRadius) * cos(lat1),
                               cos(distance / earthRadius) - sin(lat1) * sin(lat2))
        
        return CLLocationCoordinate2D(
            latitude: lat2 * 180.0 / .pi,
            longitude: lon2 * 180.0 / .pi
        )
    }
    
    /// 获取方位角（度）
    func bearing(to coordinate: CLLocationCoordinate2D) -> Double {
        let lat1 = self.latitude * .pi / 180.0
        let lat2 = coordinate.latitude * .pi / 180.0
        let deltaLon = (coordinate.longitude - self.longitude) * .pi / 180.0
        
        let y = sin(deltaLon) * cos(lat2)
        let x = cos(lat1) * sin(lat2) - sin(lat1) * cos(lat2) * cos(deltaLon)
        
        let bearing = atan2(y, x) * 180.0 / .pi
        return (bearing + 360.0).truncatingRemainder(dividingBy: 360.0)
    }
}

// MARK: - CLLocationCoordinate2D Codable

extension CLLocationCoordinate2D: Codable {
    
    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(latitude, forKey: .latitude)
        try container.encode(longitude, forKey: .longitude)
    }
    
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let latitude = try container.decode(Double.self, forKey: .latitude)
        let longitude = try container.decode(Double.self, forKey: .longitude)
        self.init(latitude: latitude, longitude: longitude)
    }
    
    private enum CodingKeys: String, CodingKey {
        case latitude
        case longitude
    }
}

// MARK: - CLLocationCoordinate2D Equatable

extension CLLocationCoordinate2D: Equatable {
    
    public static func == (lhs: CLLocationCoordinate2D, rhs: CLLocationCoordinate2D) -> Bool {
        return abs(lhs.latitude - rhs.latitude) < 0.000001 &&
               abs(lhs.longitude - rhs.longitude) < 0.000001
    }
}

// MARK: - 预定义坐标常量

extension CLLocationCoordinate2D {
    
    /// 北京天安门
    static let beijing = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
    
    /// 上海外滩
    static let shanghai = CLLocationCoordinate2D(latitude: 31.2304, longitude: 121.4737)
    
    /// 深圳腾讯大厦
    static let shenzhen = CLLocationCoordinate2D(latitude: 22.5431, longitude: 114.0579)
    
    /// 杭州西湖
    static let hangzhou = CLLocationCoordinate2D(latitude: 30.2741, longitude: 120.1551)
    
    /// 广州塔
    static let guangzhou = CLLocationCoordinate2D(latitude: 23.1088, longitude: 113.3240)
    
    /// 成都天府广场
    static let chengdu = CLLocationCoordinate2D(latitude: 30.6598, longitude: 104.0633)
    
    /// 西安钟楼
    static let xian = CLLocationCoordinate2D(latitude: 34.2583, longitude: 108.9286)
    
    /// 南京夫子庙
    static let nanjing = CLLocationCoordinate2D(latitude: 32.0473, longitude: 118.7892)
}

// MARK: - 坐标转换工具

extension CLLocationCoordinate2D {
    
    /// WGS84 转 GCJ02 (国测局坐标系)
    var toGCJ02: CLLocationCoordinate2D {
        return CoordinateConverter.wgs84ToGcj02(self)
    }
    
    /// GCJ02 转 WGS84
    var toWGS84: CLLocationCoordinate2D {
        return CoordinateConverter.gcj02ToWgs84(self)
    }
    
    /// WGS84 转 BD09 (百度坐标系)
    var toBD09: CLLocationCoordinate2D {
        return CoordinateConverter.wgs84ToBd09(self)
    }
}

// MARK: - 坐标转换器

struct CoordinateConverter {
    
    private static let a = 6378245.0
    private static let ee = 0.00669342162296594323
    
    /// WGS84 转 GCJ02
    static func wgs84ToGcj02(_ coordinate: CLLocationCoordinate2D) -> CLLocationCoordinate2D {
        if outOfChina(coordinate) {
            return coordinate
        }
        
        var dLat = transformLat(coordinate.longitude - 105.0, coordinate.latitude - 35.0)
        var dLon = transformLon(coordinate.longitude - 105.0, coordinate.latitude - 35.0)
        
        let radLat = coordinate.latitude / 180.0 * .pi
        var magic = sin(radLat)
        magic = 1 - ee * magic * magic
        let sqrtMagic = sqrt(magic)
        
        dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * .pi)
        dLon = (dLon * 180.0) / (a / sqrtMagic * cos(radLat) * .pi)
        
        return CLLocationCoordinate2D(
            latitude: coordinate.latitude + dLat,
            longitude: coordinate.longitude + dLon
        )
    }
    
    /// GCJ02 转 WGS84
    static func gcj02ToWgs84(_ coordinate: CLLocationCoordinate2D) -> CLLocationCoordinate2D {
        if outOfChina(coordinate) {
            return coordinate
        }
        
        var dLat = transformLat(coordinate.longitude - 105.0, coordinate.latitude - 35.0)
        var dLon = transformLon(coordinate.longitude - 105.0, coordinate.latitude - 35.0)
        
        let radLat = coordinate.latitude / 180.0 * .pi
        var magic = sin(radLat)
        magic = 1 - ee * magic * magic
        let sqrtMagic = sqrt(magic)
        
        dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * .pi)
        dLon = (dLon * 180.0) / (a / sqrtMagic * cos(radLat) * .pi)
        
        return CLLocationCoordinate2D(
            latitude: coordinate.latitude - dLat,
            longitude: coordinate.longitude - dLon
        )
    }
    
    /// WGS84 转 BD09
    static func wgs84ToBd09(_ coordinate: CLLocationCoordinate2D) -> CLLocationCoordinate2D {
        let gcj02 = wgs84ToGcj02(coordinate)
        return gcj02ToBd09(gcj02)
    }
    
    /// GCJ02 转 BD09
    static func gcj02ToBd09(_ coordinate: CLLocationCoordinate2D) -> CLLocationCoordinate2D {
        let z = sqrt(coordinate.longitude * coordinate.longitude + coordinate.latitude * coordinate.latitude) + 0.00002 * sin(coordinate.latitude * .pi)
        let theta = atan2(coordinate.latitude, coordinate.longitude) + 0.000003 * cos(coordinate.longitude * .pi)
        
        return CLLocationCoordinate2D(
            latitude: z * sin(theta) + 0.006,
            longitude: z * cos(theta) + 0.0065
        )
    }
    
    // MARK: - Private Methods
    
    private static func outOfChina(_ coordinate: CLLocationCoordinate2D) -> Bool {
        return coordinate.longitude < 72.004 || coordinate.longitude > 137.8347 ||
               coordinate.latitude < 0.8293 || coordinate.latitude > 55.8271
    }
    
    private static func transformLat(_ lon: Double, _ lat: Double) -> Double {
        var ret = -100.0 + 2.0 * lon + 3.0 * lat + 0.2 * lat * lat + 0.1 * lon * lat + 0.2 * sqrt(abs(lon))
        ret += (20.0 * sin(6.0 * lon * .pi) + 20.0 * sin(2.0 * lon * .pi)) * 2.0 / 3.0
        ret += (20.0 * sin(lat * .pi) + 40.0 * sin(lat / 3.0 * .pi)) * 2.0 / 3.0
        ret += (160.0 * sin(lat / 12.0 * .pi) + 320 * sin(lat * .pi / 30.0)) * 2.0 / 3.0
        return ret
    }
    
    private static func transformLon(_ lon: Double, _ lat: Double) -> Double {
        var ret = 300.0 + lon + 2.0 * lat + 0.1 * lon * lon + 0.1 * lon * lat + 0.1 * sqrt(abs(lon))
        ret += (20.0 * sin(6.0 * lon * .pi) + 20.0 * sin(2.0 * lon * .pi)) * 2.0 / 3.0
        ret += (20.0 * sin(lon * .pi) + 40.0 * sin(lon / 3.0 * .pi)) * 2.0 / 3.0
        ret += (150.0 * sin(lon / 12.0 * .pi) + 300.0 * sin(lon / 30.0 * .pi)) * 2.0 / 3.0
        return ret
    }
}
