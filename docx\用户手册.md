# 虚拟定位应用用户手册

## 📱 应用简介

虚拟定位应用是一款专业的iOS位置模拟工具，允许用户在不改变实际位置的情况下，模拟任意地理位置。应用支持单点定位、路径模拟、位置历史管理等功能，适用于应用测试、位置服务开发等场景。

### 主要功能
- 🎯 **精确虚拟定位** - 支持任意坐标的精确定位模拟
- 🛣️ **路径模拟** - 沿指定路径移动的动态定位
- 📍 **位置管理** - 历史记录、收藏位置、预设位置
- ⚙️ **个性化设置** - 精度调节、更新频率、界面偏好
- 🔍 **地址搜索** - 支持地名搜索和地址解析
- 📊 **数据导出** - 位置数据和使用统计导出

## 🚀 快速开始

### 首次使用

1. **安装应用**
   - 从App Store下载并安装虚拟定位应用
   - 确保设备运行iOS 15.5或更高版本

2. **授权位置权限**
   - 首次启动时，应用会请求位置权限
   - 选择"使用应用时允许"以获得最佳体验
   - 位置权限用于获取当前真实位置作为参考

3. **了解界面**
   - 主界面包含地图视图和控制面板
   - 地图支持缩放、拖拽和点击选择位置
   - 控制面板提供定位控制和设置选项

### 基本操作

#### 单点虚拟定位

1. **选择目标位置**
   - 在地图上点击任意位置
   - 或使用搜索功能输入地址
   - 地图会显示选中位置的标注

2. **调整定位精度**
   - 使用精度滑块调整定位精度（1-100米）
   - 较低精度模拟真实GPS的不确定性
   - 较高精度提供更准确的位置信息

3. **开始虚拟定位**
   - 点击"开始虚拟定位"按钮
   - 应用开始向系统提供虚拟位置信息
   - 状态指示器显示当前定位状态

4. **停止虚拟定位**
   - 点击"停止虚拟定位"按钮
   - 应用恢复使用真实位置信息

## 🛣️ 路径模拟功能

### 创建路径

1. **规划路径**
   - 在地图上依次点击多个位置创建路径
   - 支持添加、删除和调整路径点
   - 实时显示路径总距离和预估时间

2. **设置移动参数**
   - 调整移动速度（0.1-50 m/s）
   - 选择路径插值方式（线性/平滑）
   - 设置更新频率（0.1-5秒）

3. **开始路径模拟**
   - 点击"开始路径模拟"
   - 应用沿着指定路径移动虚拟位置
   - 实时显示移动进度和统计信息

### 路径控制

- **暂停/恢复** - 随时暂停或恢复路径模拟
- **跳转进度** - 直接跳转到路径的任意位置
- **调整速度** - 实时调整移动速度
- **查看统计** - 查看已行进距离、剩余距离、平均速度等

## 📍 位置管理

### 历史记录

- **自动记录** - 应用自动保存使用过的虚拟位置
- **查看详情** - 点击历史记录查看位置详细信息
- **快速使用** - 直接选择历史位置进行定位
- **删除记录** - 滑动删除不需要的历史记录

### 收藏位置

- **添加收藏** - 长按历史记录或点击收藏按钮
- **管理收藏** - 在收藏列表中查看和管理收藏位置
- **快速访问** - 收藏位置可快速选择使用

### 预设位置

- **常用位置** - 应用预设了一些常用的测试位置
- **全球覆盖** - 包含世界各大城市的坐标
- **一键使用** - 直接选择预设位置进行定位

## ⚙️ 设置和个性化

### 位置设置

- **默认精度** - 设置新建虚拟位置的默认精度
- **更新频率** - 调整位置更新的时间间隔
- **自动启动** - 选择位置后自动开始虚拟定位

### 地图设置

- **地图类型** - 选择标准、卫星或混合地图
- **显示用户位置** - 是否在地图上显示真实位置

### 通用设置

- **触觉反馈** - 开启或关闭操作时的触觉反馈
- **历史记录上限** - 设置保存的历史记录数量（10-200条）

### 数据管理

- **清空历史记录** - 删除所有位置历史记录
- **导出数据** - 将位置数据导出为JSON文件
- **导入数据** - 从文件导入位置数据
- **清空所有数据** - 重置应用到初始状态

## 🔍 搜索功能

### 地址搜索

1. **打开搜索** - 点击搜索按钮或搜索栏
2. **输入地址** - 输入地名、地址或坐标
3. **选择结果** - 从搜索结果中选择目标位置
4. **自动定位** - 地图自动移动到搜索位置

### 搜索技巧

- **支持中英文** - 可以使用中文或英文进行搜索
- **模糊匹配** - 支持部分地名的模糊搜索
- **坐标输入** - 直接输入经纬度坐标（格式：纬度,经度）
- **历史搜索** - 自动保存搜索历史便于重复使用

## 📊 数据和统计

### 使用统计

- **定位次数** - 查看虚拟定位的使用次数
- **使用时长** - 统计虚拟定位的总使用时间
- **路径统计** - 查看路径模拟的距离和时间统计
- **位置分布** - 分析使用过的位置地理分布

### 数据导出

- **位置数据** - 导出所有位置记录为JSON格式
- **使用报告** - 生成详细的使用统计报告
- **路径数据** - 导出路径模拟的轨迹数据
- **设置备份** - 备份应用设置和偏好

## 🔒 隐私和安全

### 数据保护

- **本地存储** - 所有位置数据仅存储在设备本地
- **无网络传输** - 不会向服务器上传任何位置信息
- **加密存储** - 敏感数据使用加密方式存储
- **权限最小化** - 仅请求必要的系统权限

### 使用建议

- **合法使用** - 请确保在法律允许的范围内使用
- **测试目的** - 主要用于应用开发和测试
- **避免欺骗** - 不要用于欺骗商业服务或进行非法活动
- **自担风险** - 用户需自行承担使用风险

## ❓ 常见问题

### Q: 为什么虚拟定位不生效？
A: 请检查以下几点：
- 确保已授予位置权限
- 检查是否正确选择了目标位置
- 确认已点击"开始虚拟定位"按钮
- 重启应用或设备可能有帮助

### Q: 如何提高定位精度？
A: 可以通过以下方式：
- 调整精度滑块到较小值（1-5米）
- 减少位置更新频率
- 选择更精确的目标坐标

### Q: 路径模拟速度太快或太慢？
A: 可以实时调整：
- 使用速度设置调整移动速度
- 暂停后重新设置参数
- 根据路径长度选择合适的速度

### Q: 如何备份我的位置数据？
A: 使用数据导出功能：
- 进入设置 → 数据管理
- 选择"导出数据"
- 保存导出的JSON文件

### Q: 应用占用存储空间过大？
A: 可以清理数据：
- 定期清理历史记录
- 删除不需要的收藏位置
- 使用"清空所有数据"重置应用

## 📞 技术支持

### 联系方式
- **邮箱支持**: <EMAIL>
- **在线帮助**: https://help.virtuallocation.app
- **用户社区**: https://community.virtuallocation.app

### 反馈建议
- **功能建议**: 通过应用内反馈功能提交
- **问题报告**: 详细描述问题和复现步骤
- **评价应用**: 在App Store留下您的评价和建议

---

**版本**: 1.0.0  
**更新日期**: 2025年6月20日  
**兼容性**: iOS 15.5+  
**语言**: 简体中文、English
