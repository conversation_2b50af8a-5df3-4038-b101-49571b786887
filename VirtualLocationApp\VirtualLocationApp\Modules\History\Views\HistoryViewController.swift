//
//  HistoryViewController.swift
//  VirtualLocationApp
//
//  Created by Developer on 2025-06-20.
//  Copyright © 2025 YourCompany. All rights reserved.
//

import UIKit
import CoreLocation

class HistoryViewController: UIViewController {
    
    // MARK: - UI Elements
    
    private let segmentedControl = UISegmentedControl(items: ["历史记录", "收藏位置", "预设位置"])
    private let tableView = UITableView(frame: .zero, style: .insetGrouped)
    private let emptyStateView = EmptyStateView()
    
    // MARK: - Properties
    
    private let dataService = DataService.shared
    private var currentSegment: HistorySegment = .history
    private var historyLocations: [VirtualLocationModel] = []
    private var favoriteLocations: [VirtualLocationModel] = []
    private var predefinedLocations: [VirtualLocationModel] = []
    
    weak var delegate: HistoryViewControllerDelegate?
    
    // MARK: - Enums
    
    enum HistorySegment: Int, CaseIterable {
        case history = 0
        case favorites
        case predefined
        
        var title: String {
            switch self {
            case .history:
                return "历史记录"
            case .favorites:
                return "收藏位置"
            case .predefined:
                return "预设位置"
            }
        }
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupTableView()
        loadData()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadData()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        title = "位置管理"
        view.backgroundColor = .systemBackground
        
        // 设置导航栏按钮
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .done,
            target: self,
            action: #selector(doneButtonTapped)
        )
        
        // 设置分段控制器
        segmentedControl.selectedSegmentIndex = 0
        segmentedControl.addTarget(self, action: #selector(segmentChanged(_:)), for: .valueChanged)
        
        // 设置布局
        setupLayout()
    }
    
    private func setupLayout() {
        segmentedControl.translatesAutoresizingMaskIntoConstraints = false
        tableView.translatesAutoresizingMaskIntoConstraints = false
        emptyStateView.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(segmentedControl)
        view.addSubview(tableView)
        view.addSubview(emptyStateView)
        
        NSLayoutConstraint.activate([
            segmentedControl.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 16),
            segmentedControl.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 16),
            segmentedControl.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -16),
            
            tableView.topAnchor.constraint(equalTo: segmentedControl.bottomAnchor, constant: 16),
            tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            emptyStateView.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            emptyStateView.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            emptyStateView.leadingAnchor.constraint(greaterThanOrEqualTo: view.leadingAnchor, constant: 32),
            emptyStateView.trailingAnchor.constraint(lessThanOrEqualTo: view.trailingAnchor, constant: -32)
        ])
    }
    
    private func setupTableView() {
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(LocationTableViewCell.self, forCellReuseIdentifier: "LocationCell")
        tableView.rowHeight = UITableView.automaticDimension
        tableView.estimatedRowHeight = 80
    }
    
    // MARK: - Data Loading
    
    private func loadData() {
        historyLocations = dataService.getLocationHistory()
        favoriteLocations = dataService.getFavoriteLocations()
        predefinedLocations = dataService.getPredefinedLocations()
        
        updateUI()
    }
    
    private func updateUI() {
        let currentData = getCurrentData()
        let isEmpty = currentData.isEmpty
        
        tableView.isHidden = isEmpty
        emptyStateView.isHidden = !isEmpty
        
        if isEmpty {
            updateEmptyState()
        }
        
        tableView.reloadData()
    }
    
    private func getCurrentData() -> [VirtualLocationModel] {
        switch currentSegment {
        case .history:
            return historyLocations
        case .favorites:
            return favoriteLocations
        case .predefined:
            return predefinedLocations
        }
    }
    
    private func updateEmptyState() {
        switch currentSegment {
        case .history:
            emptyStateView.configure(
                image: UIImage(systemName: "clock"),
                title: "暂无历史记录",
                message: "使用虚拟定位后，位置记录将显示在这里"
            )
        case .favorites:
            emptyStateView.configure(
                image: UIImage(systemName: "heart"),
                title: "暂无收藏位置",
                message: "长按历史记录中的位置可以添加到收藏"
            )
        case .predefined:
            emptyStateView.configure(
                image: UIImage(systemName: "map"),
                title: "预设位置",
                message: "这里显示常用的预设位置"
            )
        }
    }
    
    // MARK: - Actions
    
    @objc private func doneButtonTapped() {
        dismiss(animated: true)
    }
    
    @objc private func segmentChanged(_ sender: UISegmentedControl) {
        currentSegment = HistorySegment(rawValue: sender.selectedSegmentIndex) ?? .history
        updateUI()
    }
    
    // MARK: - Helper Methods
    
    private func selectLocation(_ location: VirtualLocationModel) {
        delegate?.historyViewController(self, didSelectLocation: location)
        dismiss(animated: true)
    }
    
    private func toggleFavorite(_ location: VirtualLocationModel) {
        if dataService.isLocationFavorited(withId: location.id) {
            dataService.deleteFavoriteLocation(withId: location.id)
            showToast("已从收藏中移除")
        } else {
            dataService.saveFavoriteLocation(location)
            showToast("已添加到收藏")
        }
        loadData()
    }
    
    private func deleteLocation(_ location: VirtualLocationModel) {
        switch currentSegment {
        case .history:
            dataService.deleteLocationFromHistory(withId: location.id)
            showToast("已删除历史记录")
        case .favorites:
            dataService.deleteFavoriteLocation(withId: location.id)
            showToast("已从收藏中移除")
        case .predefined:
            // 预设位置不允许删除
            return
        }
        loadData()
    }
    
    private func showToast(_ message: String) {
        let alert = UIAlertController(title: nil, message: message, preferredStyle: .alert)
        present(alert, animated: true)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            alert.dismiss(animated: true)
        }
    }
}

// MARK: - UITableViewDataSource

extension HistoryViewController: UITableViewDataSource {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return getCurrentData().count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "LocationCell", for: indexPath) as! LocationTableViewCell
        let location = getCurrentData()[indexPath.row]
        let isFavorited = dataService.isLocationFavorited(withId: location.id)
        
        cell.configure(with: location, isFavorited: isFavorited, showFavoriteButton: currentSegment != .predefined)
        cell.delegate = self
        
        return cell
    }
}

// MARK: - UITableViewDelegate

extension HistoryViewController: UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let location = getCurrentData()[indexPath.row]
        selectLocation(location)
    }
    
    func tableView(_ tableView: UITableView, trailingSwipeActionsConfigurationForRowAt indexPath: IndexPath) -> UISwipeActionsConfiguration? {
        let location = getCurrentData()[indexPath.row]
        var actions: [UIContextualAction] = []
        
        // 删除操作（预设位置除外）
        if currentSegment != .predefined {
            let deleteAction = UIContextualAction(style: .destructive, title: "删除") { [weak self] _, _, completion in
                self?.deleteLocation(location)
                completion(true)
            }
            deleteAction.image = UIImage(systemName: "trash")
            actions.append(deleteAction)
        }
        
        // 收藏/取消收藏操作
        let isFavorited = dataService.isLocationFavorited(withId: location.id)
        let favoriteAction = UIContextualAction(
            style: .normal,
            title: isFavorited ? "取消收藏" : "收藏"
        ) { [weak self] _, _, completion in
            self?.toggleFavorite(location)
            completion(true)
        }
        favoriteAction.backgroundColor = isFavorited ? .systemOrange : .systemBlue
        favoriteAction.image = UIImage(systemName: isFavorited ? "heart.slash" : "heart")
        actions.append(favoriteAction)
        
        return UISwipeActionsConfiguration(actions: actions)
    }
}

// MARK: - LocationTableViewCellDelegate

extension HistoryViewController: LocationTableViewCellDelegate {
    
    func locationTableViewCell(_ cell: LocationTableViewCell, didTapFavoriteButton location: VirtualLocationModel) {
        toggleFavorite(location)
    }
}

// MARK: - HistoryViewControllerDelegate

protocol HistoryViewControllerDelegate: AnyObject {
    func historyViewController(_ controller: HistoryViewController, didSelectLocation location: VirtualLocationModel)
}
