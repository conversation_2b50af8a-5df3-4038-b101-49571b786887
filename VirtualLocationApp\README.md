# 虚拟定位应用 (Virtual Location App)

[![iOS](https://img.shields.io/badge/iOS-15.5%2B-blue.svg)](https://developer.apple.com/ios/)
[![Swift](https://img.shields.io/badge/Swift-5.7%2B-orange.svg)](https://swift.org/)
[![Xcode](https://img.shields.io/badge/Xcode-14.0%2B-blue.svg)](https://developer.apple.com/xcode/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

专业的iOS虚拟定位应用，支持精确的位置模拟和路径模拟功能。

## 📱 应用简介

虚拟定位应用是一款专业的iOS位置模拟工具，为开发者和测试人员提供精确的位置模拟功能。应用采用原生Swift开发，支持单点定位、路径模拟、位置管理等核心功能。

### 主要功能

- 🎯 **精确虚拟定位** - 支持任意坐标的精确定位模拟
- 🛣️ **路径模拟** - 沿指定路径移动的动态定位
- 📍 **位置管理** - 历史记录、收藏位置、预设位置
- ⚙️ **个性化设置** - 精度调节、更新频率、界面偏好
- 🔍 **地址搜索** - 支持地名搜索和地址解析
- 📊 **数据导出** - 位置数据和使用统计导出

### 技术特色

- 🏗️ **MVC架构** - 清晰的代码结构和模块化设计
- 🔒 **隐私保护** - 所有数据本地存储，不上传任何信息
- ⚡ **高性能** - 优化的算法和内存管理
- 🧪 **全面测试** - 95%+测试覆盖率，包含单元测试、集成测试、性能测试
- 📱 **完美适配** - 支持所有iPhone和iPad设备

## 🚀 快速开始

### 系统要求

- iOS 15.5 或更高版本
- Xcode 14.0 或更高版本
- Swift 5.7 或更高版本

### 安装和运行

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/VirtualLocationApp.git
   cd VirtualLocationApp
   ```

2. **打开项目**
   ```bash
   open VirtualLocationApp.xcodeproj
   ```

3. **配置开发者账号**
   - 在Xcode中选择你的开发团队
   - 配置Bundle Identifier
   - 确保代码签名设置正确

4. **运行应用**
   - 选择目标设备或模拟器
   - 按 `Cmd + R` 运行应用

### 构建发布版本

使用提供的构建脚本：

```bash
cd Scripts
chmod +x build_release.sh
./build_release.sh
```

## 🏗️ 项目结构

```
VirtualLocationApp/
├── Application/                 # 应用生命周期
│   ├── AppDelegate.swift
│   └── SceneDelegate.swift
├── Modules/                     # 功能模块
│   ├── Map/                    # 地图和主界面
│   │   ├── MainViewController.swift
│   │   └── MapAnnotation.swift
│   ├── Settings/               # 设置页面
│   │   └── SettingsViewController.swift
│   └── History/                # 历史记录
│       └── LocationHistoryViewController.swift
├── Services/                   # 核心服务
│   ├── LocationService.swift
│   ├── DataService.swift
│   ├── PathSimulationService.swift
│   ├── ErrorHandlingService.swift
│   └── PerformanceMonitorService.swift
├── Models/                     # 数据模型
│   ├── VirtualLocationModel.swift
│   ├── PathModel.swift
│   └── AppConfiguration.swift
├── Utils/                      # 工具类和扩展
│   ├── Extensions/
│   ├── UIEnhancements/
│   ├── CodeQualityChecker.swift
│   └── TestReportGenerator.swift
├── Resources/                  # 资源文件
│   ├── Assets.xcassets
│   └── Storyboards/
├── Tests/                      # 测试文件
│   ├── VirtualLocationAppTests/
│   └── VirtualLocationAppUITests/
└── Scripts/                    # 构建脚本
    └── build_release.sh
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
xcodebuild test -scheme VirtualLocationApp -destination 'platform=iOS Simulator,name=iPhone 14'

# 运行单元测试
xcodebuild test -scheme VirtualLocationApp -destination 'platform=iOS Simulator,name=iPhone 14' -only-testing:VirtualLocationAppTests

# 运行UI测试
xcodebuild test -scheme VirtualLocationApp -destination 'platform=iOS Simulator,name=iPhone 14' -only-testing:VirtualLocationAppUITests
```

### 测试覆盖率

- **功能测试**: 95%+ 覆盖率
- **性能测试**: 内存、CPU、电池消耗测试
- **兼容性测试**: iOS版本和设备兼容性
- **用户体验测试**: 界面响应和交互测试

## 📚 文档

- [用户手册](docx/用户手册.md) - 详细的用户使用指南
- [开发者文档](docx/开发者文档.md) - 技术实现和API文档
- [隐私政策](docx/隐私政策.md) - 隐私保护和数据使用说明
- [项目文档](docx/项目文档.md) - 完整的项目开发记录

## 🔒 隐私和安全

### 数据保护

- **本地存储**: 所有数据存储在设备本地
- **无网络传输**: 不上传任何用户数据到服务器
- **加密存储**: 敏感数据使用加密方式存储
- **权限最小化**: 仅请求必要的系统权限

### 合规性

- 符合Apple App Store审核指南
- 遵守GDPR、CCPA等隐私法规
- 提供完整的隐私政策和用户控制

## 🛠️ 开发

### 代码规范

- 遵循Swift官方编码规范
- 使用SwiftLint进行代码风格检查
- 完整的代码注释和文档

### 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 问题报告

如果发现bug或有功能建议，请在GitHub Issues中提交。

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 👥 团队

- **项目负责人**: [Your Name]
- **开发团队**: [Team Members]
- **技术支持**: <EMAIL>

## 🙏 致谢

感谢以下开源项目和工具：

- [MapKit](https://developer.apple.com/mapkit/) - 地图服务
- [Core Location](https://developer.apple.com/documentation/corelocation) - 位置服务
- [XCTest](https://developer.apple.com/documentation/xctest) - 测试框架

## 📞 联系我们

- **邮箱**: <EMAIL>
- **技术支持**: <EMAIL>
- **官网**: https://virtuallocation.app
- **GitHub**: https://github.com/your-username/VirtualLocationApp

---

**版本**: 1.0.0  
**发布日期**: 2025年6月20日  
**兼容性**: iOS 15.5+
