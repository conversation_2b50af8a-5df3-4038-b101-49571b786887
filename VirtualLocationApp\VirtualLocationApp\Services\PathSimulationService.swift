//
//  PathSimulationService.swift
//  VirtualLocationApp
//
//  Created by <PERSON><PERSON><PERSON> on 2025-06-20.
//  Copyright © 2025 YourCompany. All rights reserved.
//

import Foundation
import CoreLocation

// MARK: - Path Simulation Delegate

protocol PathSimulationDelegate: AnyObject {
    func pathSimulation(_ service: PathSimulationService, didUpdateLocation location: CLLocation)
    func pathSimulation(_ service: PathSimulationService, didCompleteWithTotalDistance distance: CLLocationDistance)
    func pathSimulation(_ service: PathSimulationService, didFailWithError error: Error)
}

// MARK: - Path Simulation Service

class PathSimulationService {
    
    // MARK: - Properties
    
    weak var delegate: PathSimulationDelegate?
    
    private var pathPoints: [CLLocationCoordinate2D] = []
    private var currentIndex: Int = 0
    private var simulationTimer: Timer?
    private var isSimulating: Bool = false
    private var simulationSpeed: CLLocationSpeed = 5.0 // 默认5m/s
    private var updateInterval: TimeInterval = 1.0
    private var totalDistance: CLLocationDistance = 0
    private var startTime: Date?
    
    // MARK: - Public Properties
    
    var isActive: Bool {
        return isSimulating
    }
    
    var progress: Double {
        guard !pathPoints.isEmpty else { return 0.0 }
        return Double(currentIndex) / Double(pathPoints.count - 1)
    }
    
    var remainingDistance: CLLocationDistance {
        guard currentIndex < pathPoints.count - 1 else { return 0.0 }
        
        var distance: CLLocationDistance = 0
        for i in currentIndex..<(pathPoints.count - 1) {
            let location1 = CLLocation(latitude: pathPoints[i].latitude, longitude: pathPoints[i].longitude)
            let location2 = CLLocation(latitude: pathPoints[i + 1].latitude, longitude: pathPoints[i + 1].longitude)
            distance += location1.distance(from: location2)
        }
        return distance
    }
    
    // MARK: - Public Methods
    
    /// 开始路径模拟
    func startPathSimulation(path: [CLLocationCoordinate2D], speed: CLLocationSpeed = 5.0) {
        guard !path.isEmpty else {
            delegate?.pathSimulation(self, didFailWithError: PathSimulationError.emptyPath)
            return
        }
        
        guard !isSimulating else {
            delegate?.pathSimulation(self, didFailWithError: PathSimulationError.alreadySimulating)
            return
        }
        
        pathPoints = path
        currentIndex = 0
        simulationSpeed = max(0.1, min(50.0, speed)) // 限制速度在0.1-50m/s之间
        isSimulating = true
        startTime = Date()
        totalDistance = calculateTotalDistance()
        
        // 立即发送第一个位置
        sendCurrentLocation()
        
        // 启动定时器
        startSimulationTimer()
        
        print("✅ 路径模拟已启动，总距离: \(String(format: "%.2f", totalDistance))米")
    }
    
    /// 停止路径模拟
    func stopPathSimulation() {
        guard isSimulating else { return }
        
        stopSimulationTimer()
        isSimulating = false
        
        print("✅ 路径模拟已停止")
    }
    
    /// 暂停路径模拟
    func pausePathSimulation() {
        guard isSimulating else { return }
        stopSimulationTimer()
        print("⏸️ 路径模拟已暂停")
    }
    
    /// 恢复路径模拟
    func resumePathSimulation() {
        guard isSimulating else { return }
        startSimulationTimer()
        print("▶️ 路径模拟已恢复")
    }
    
    /// 设置模拟速度
    func setSimulationSpeed(_ speed: CLLocationSpeed) {
        simulationSpeed = max(0.1, min(50.0, speed))
        
        // 如果正在模拟，重启定时器以应用新速度
        if isSimulating && simulationTimer != nil {
            stopSimulationTimer()
            startSimulationTimer()
        }
    }
    
    /// 跳转到指定进度
    func seekToProgress(_ progress: Double) {
        guard !pathPoints.isEmpty else { return }
        
        let clampedProgress = max(0.0, min(1.0, progress))
        currentIndex = Int(clampedProgress * Double(pathPoints.count - 1))
        
        if isSimulating {
            sendCurrentLocation()
        }
    }
    
    /// 获取模拟统计信息
    func getSimulationStats() -> PathSimulationStats? {
        guard let startTime = startTime else { return nil }
        
        let elapsedTime = Date().timeIntervalSince(startTime)
        let completedDistance = totalDistance - remainingDistance
        let averageSpeed = elapsedTime > 0 ? completedDistance / elapsedTime : 0
        
        return PathSimulationStats(
            totalDistance: totalDistance,
            completedDistance: completedDistance,
            remainingDistance: remainingDistance,
            elapsedTime: elapsedTime,
            averageSpeed: averageSpeed,
            currentSpeed: simulationSpeed,
            progress: progress
        )
    }
    
    // MARK: - Private Methods
    
    private func startSimulationTimer() {
        simulationTimer = Timer.scheduledTimer(withTimeInterval: updateInterval, repeats: true) { [weak self] _ in
            self?.updateSimulation()
        }
    }
    
    private func stopSimulationTimer() {
        simulationTimer?.invalidate()
        simulationTimer = nil
    }
    
    private func updateSimulation() {
        guard currentIndex < pathPoints.count - 1 else {
            // 路径完成
            completeSimulation()
            return
        }
        
        // 计算下一个位置
        let nextIndex = moveToNextPosition()
        currentIndex = nextIndex
        
        // 发送当前位置
        sendCurrentLocation()
    }
    
    private func moveToNextPosition() -> Int {
        let currentPoint = pathPoints[currentIndex]
        let nextPoint = pathPoints[currentIndex + 1]
        
        let currentLocation = CLLocation(latitude: currentPoint.latitude, longitude: currentPoint.longitude)
        let nextLocation = CLLocation(latitude: nextPoint.latitude, longitude: nextPoint.longitude)
        
        let segmentDistance = currentLocation.distance(from: nextLocation)
        let distanceToMove = simulationSpeed * updateInterval
        
        if distanceToMove >= segmentDistance {
            // 移动到下一个点
            return currentIndex + 1
        } else {
            // 在当前段内插值
            let ratio = distanceToMove / segmentDistance
            let interpolatedCoordinate = interpolateCoordinate(
                from: currentPoint,
                to: nextPoint,
                ratio: ratio
            )
            
            // 更新当前点为插值点
            pathPoints[currentIndex] = interpolatedCoordinate
            return currentIndex
        }
    }
    
    private func interpolateCoordinate(from start: CLLocationCoordinate2D, to end: CLLocationCoordinate2D, ratio: Double) -> CLLocationCoordinate2D {
        let lat = start.latitude + (end.latitude - start.latitude) * ratio
        let lon = start.longitude + (end.longitude - start.longitude) * ratio
        return CLLocationCoordinate2D(latitude: lat, longitude: lon)
    }
    
    private func sendCurrentLocation() {
        guard currentIndex < pathPoints.count else { return }
        
        let coordinate = pathPoints[currentIndex]
        let location = CLLocation(
            coordinate: coordinate,
            altitude: 0,
            horizontalAccuracy: 5.0,
            verticalAccuracy: 5.0,
            course: calculateCourse(),
            speed: simulationSpeed,
            timestamp: Date()
        )
        
        delegate?.pathSimulation(self, didUpdateLocation: location)
    }
    
    private func calculateCourse() -> CLLocationDirection {
        guard currentIndex < pathPoints.count - 1 else { return -1.0 }
        
        let currentPoint = pathPoints[currentIndex]
        let nextPoint = pathPoints[currentIndex + 1]
        
        return currentPoint.bearing(to: nextPoint)
    }
    
    private func calculateTotalDistance() -> CLLocationDistance {
        guard pathPoints.count > 1 else { return 0.0 }
        
        var distance: CLLocationDistance = 0
        for i in 0..<(pathPoints.count - 1) {
            let location1 = CLLocation(latitude: pathPoints[i].latitude, longitude: pathPoints[i].longitude)
            let location2 = CLLocation(latitude: pathPoints[i + 1].latitude, longitude: pathPoints[i + 1].longitude)
            distance += location1.distance(from: location2)
        }
        return distance
    }
    
    private func completeSimulation() {
        stopSimulationTimer()
        isSimulating = false
        delegate?.pathSimulation(self, didCompleteWithTotalDistance: totalDistance)
        print("✅ 路径模拟完成，总距离: \(String(format: "%.2f", totalDistance))米")
    }
}

// MARK: - Path Simulation Stats

struct PathSimulationStats {
    let totalDistance: CLLocationDistance
    let completedDistance: CLLocationDistance
    let remainingDistance: CLLocationDistance
    let elapsedTime: TimeInterval
    let averageSpeed: CLLocationSpeed
    let currentSpeed: CLLocationSpeed
    let progress: Double
    
    var formattedTotalDistance: String {
        return String(format: "%.2f米", totalDistance)
    }
    
    var formattedElapsedTime: String {
        let minutes = Int(elapsedTime) / 60
        let seconds = Int(elapsedTime) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    var formattedAverageSpeed: String {
        return String(format: "%.1f m/s", averageSpeed)
    }
    
    var formattedProgress: String {
        return String(format: "%.1f%%", progress * 100)
    }
}

// MARK: - Path Simulation Error

enum PathSimulationError: Error, LocalizedError {
    case emptyPath
    case alreadySimulating
    case invalidSpeed
    case simulationNotActive
    
    var errorDescription: String? {
        switch self {
        case .emptyPath:
            return "路径为空"
        case .alreadySimulating:
            return "路径模拟已在进行中"
        case .invalidSpeed:
            return "无效的模拟速度"
        case .simulationNotActive:
            return "路径模拟未激活"
        }
    }
}
