//
//  UserExperienceTests.swift
//  VirtualLocationAppTests
//
//  Created by Developer on 2025-06-20.
//  Copyright © 2025 YourCompany. All rights reserved.
//

import XCTest
import UIKit
import CoreLocation
@testable import VirtualLocationApp

class UserExperienceTests: XCTestCase {
    
    // MARK: - Test Properties
    
    var mainViewController: MainViewController!
    var animationHelper: AnimationHelper.Type!
    var hapticHelper: HapticFeedbackHelper.Type!
    var toastHelper: ToastHelper.Type!
    
    // MARK: - Setup and Teardown
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        
        // 创建主视图控制器
        let storyboard = UIStoryboard(name: "Main", bundle: Bundle.main)
        mainViewController = storyboard.instantiateViewController(withIdentifier: "MainViewController") as? MainViewController
        
        // 加载视图
        mainViewController.loadViewIfNeeded()
        
        // 设置辅助类
        animationHelper = AnimationHelper.self
        hapticHelper = HapticFeedbackHelper.self
        toastHelper = ToastHelper.self
    }
    
    override func tearDownWithError() throws {
        mainViewController = nil
        animationHelper = nil
        hapticHelper = nil
        toastHelper = nil
        
        try super.tearDownWithError()
    }
    
    // MARK: - Interface Response Tests
    
    func testInterfaceResponseTime() {
        // 测试界面响应时间
        let expectation = XCTestExpectation(description: "界面响应测试")
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        DispatchQueue.main.async {
            // 模拟用户交互
            self.mainViewController.viewDidLoad()
            self.mainViewController.viewWillAppear(true)
            self.mainViewController.viewDidAppear(true)
            
            let responseTime = CFAbsoluteTimeGetCurrent() - startTime
            
            print("📱 界面响应时间: \(String(format: "%.3f秒", responseTime))")
            
            // 验证响应时间在合理范围内
            XCTAssertLessThan(responseTime, 0.5, "界面响应时间应该小于0.5秒")
            
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    func testButtonResponseTime() {
        // 测试按钮响应时间
        let button = UIButton(type: .system)
        button.setTitle("测试按钮", for: .normal)
        
        var responseTime: TimeInterval = 0
        let expectation = XCTestExpectation(description: "按钮响应测试")
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        button.addTarget(self, action: #selector(buttonTapped), for: .touchUpInside)
        
        // 模拟按钮点击
        DispatchQueue.main.async {
            button.sendActions(for: .touchUpInside)
            responseTime = CFAbsoluteTimeGetCurrent() - startTime
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 1.0)
        
        print("📱 按钮响应时间: \(String(format: "%.3f秒", responseTime))")
        XCTAssertLessThan(responseTime, 0.1, "按钮响应时间应该小于0.1秒")
    }
    
    @objc private func buttonTapped() {
        // 按钮点击处理
    }
    
    // MARK: - Animation Smoothness Tests
    
    func testAnimationSmoothness() {
        // 测试动画流畅度
        let testView = UIView(frame: CGRect(x: 0, y: 0, width: 100, height: 100))
        testView.backgroundColor = .blue
        
        let expectation = XCTestExpectation(description: "动画流畅度测试")
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        // 测试淡入动画
        AnimationHelper.fadeIn(testView, duration: 0.3) {
            let animationTime = CFAbsoluteTimeGetCurrent() - startTime
            
            print("📱 淡入动画时间: \(String(format: "%.3f秒", animationTime))")
            
            // 验证动画时间接近预期
            XCTAssertEqual(animationTime, 0.3, accuracy: 0.1, "动画时间应该接近预期值")
            
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 1.0)
    }
    
    func testButtonAnimations() {
        // 测试按钮动画
        let button = UIButton(type: .system)
        button.frame = CGRect(x: 0, y: 0, width: 100, height: 44)
        button.setTitle("测试", for: .normal)
        
        let expectation = XCTestExpectation(description: "按钮动画测试")
        
        // 测试按钮点击动画
        AnimationHelper.animateButtonTap(button) {
            // 验证动画完成
            XCTAssertEqual(button.transform, .identity, "动画完成后按钮应该恢复原始状态")
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 1.0)
    }
    
    func testComplexAnimations() {
        // 测试复杂动画
        let testView = UIView(frame: CGRect(x: 0, y: 0, width: 100, height: 100))
        
        let expectation = XCTestExpectation(description: "复杂动画测试")
        expectation.expectedFulfillmentCount = 3
        
        // 测试弹跳动画
        AnimationHelper.bounce(testView) {
            expectation.fulfill()
        }
        
        // 测试摇摆动画
        AnimationHelper.shake(testView) {
            expectation.fulfill()
        }
        
        // 测试滑入动画
        AnimationHelper.slideIn(testView, from: .top) {
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 3.0)
    }
    
    // MARK: - Haptic Feedback Tests
    
    func testHapticFeedback() {
        // 测试触觉反馈
        print("📱 测试触觉反馈功能")
        
        // 测试不同类型的触觉反馈
        HapticFeedbackHelper.lightImpact()
        HapticFeedbackHelper.mediumImpact()
        HapticFeedbackHelper.heavyImpact()
        
        HapticFeedbackHelper.success()
        HapticFeedbackHelper.warning()
        HapticFeedbackHelper.error()
        
        HapticFeedbackHelper.selection()
        
        // 触觉反馈无法直接验证，但确保不会崩溃
        XCTAssertTrue(true, "触觉反馈应该正常执行")
    }
    
    func testHapticFeedbackTiming() {
        // 测试触觉反馈时机
        let expectation = XCTestExpectation(description: "触觉反馈时机测试")
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        HapticFeedbackHelper.success()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            let responseTime = CFAbsoluteTimeGetCurrent() - startTime
            
            print("📱 触觉反馈响应时间: \(String(format: "%.3f秒", responseTime))")
            
            // 验证触觉反馈及时响应
            XCTAssertLessThan(responseTime, 0.2, "触觉反馈应该及时响应")
            
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 1.0)
    }
    
    // MARK: - Toast and Alert Tests
    
    func testToastDisplay() {
        // 测试Toast显示
        let testView = UIView(frame: CGRect(x: 0, y: 0, width: 375, height: 667))
        
        let expectation = XCTestExpectation(description: "Toast显示测试")
        
        ToastHelper.showToast(message: "测试消息", in: testView, duration: 1.0)
        
        // 验证Toast视图被添加
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            let toastViews = testView.subviews.filter { $0.tag == 999 }
            XCTAssertEqual(toastViews.count, 1, "应该有一个Toast视图")
            
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    func testToastAutoHide() {
        // 测试Toast自动隐藏
        let testView = UIView(frame: CGRect(x: 0, y: 0, width: 375, height: 667))
        
        let expectation = XCTestExpectation(description: "Toast自动隐藏测试")
        
        ToastHelper.showToast(message: "自动隐藏测试", in: testView, duration: 0.5)
        
        // 验证Toast在指定时间后消失
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let toastViews = testView.subviews.filter { $0.tag == 999 }
            XCTAssertEqual(toastViews.count, 0, "Toast应该已经消失")
            
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    func testErrorHandlingUserExperience() {
        // 测试错误处理用户体验
        let errorHandler = ErrorHandlingService.shared
        
        // 清理之前的错误日志
        errorHandler.clearErrorLog()
        
        // 模拟错误处理
        let testError = LocationError.invalidCoordinate
        errorHandler.handleError(testError, context: "用户体验测试", showToUser: false)
        
        // 验证错误被正确记录
        let errorLog = errorHandler.getErrorLog()
        XCTAssertEqual(errorLog.count, 1, "错误应该被记录")
        
        // 验证错误信息的用户友好性
        let errorDescription = testError.localizedDescription
        XCTAssertFalse(errorDescription.isEmpty, "错误描述不应为空")
        XCTAssertTrue(errorDescription.contains("坐标"), "错误描述应该用户友好")
        
        print("📱 错误描述: \(errorDescription)")
    }
    
    // MARK: - Accessibility Tests
    
    func testAccessibilitySupport() {
        // 测试无障碍支持
        let button = UIButton(type: .system)
        button.setTitle("开始虚拟定位", for: .normal)
        button.accessibilityLabel = "开始虚拟定位按钮"
        button.accessibilityHint = "点击开始虚拟定位功能"
        
        // 验证无障碍属性
        XCTAssertTrue(button.isAccessibilityElement, "按钮应该是无障碍元素")
        XCTAssertEqual(button.accessibilityLabel, "开始虚拟定位按钮", "无障碍标签应该正确")
        XCTAssertEqual(button.accessibilityHint, "点击开始虚拟定位功能", "无障碍提示应该正确")
        
        // 测试无障碍特征
        button.accessibilityTraits = .button
        XCTAssertTrue(button.accessibilityTraits.contains(.button), "应该包含按钮特征")
    }
    
    func testVoiceOverSupport() {
        // 测试VoiceOver支持
        let label = UILabel()
        label.text = "纬度: 39.904200, 经度: 116.407400"
        label.accessibilityLabel = "当前位置：北纬39度，东经116度"
        
        XCTAssertTrue(label.isAccessibilityElement, "标签应该是无障碍元素")
        XCTAssertNotNil(label.accessibilityLabel, "应该有无障碍标签")
        
        // 验证坐标信息的可读性
        let accessibilityText = label.accessibilityLabel ?? ""
        XCTAssertTrue(accessibilityText.contains("北纬"), "应该包含可读的纬度信息")
        XCTAssertTrue(accessibilityText.contains("东经"), "应该包含可读的经度信息")
    }
    
    // MARK: - Gesture Recognition Tests
    
    func testGestureRecognition() {
        // 测试手势识别
        let testView = UIView(frame: CGRect(x: 0, y: 0, width: 300, height: 300))
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap))
        testView.addGestureRecognizer(tapGesture)
        
        let pinchGesture = UIPinchGestureRecognizer(target: self, action: #selector(handlePinch))
        testView.addGestureRecognizer(pinchGesture)
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan))
        testView.addGestureRecognizer(panGesture)
        
        // 验证手势识别器被正确添加
        XCTAssertEqual(testView.gestureRecognizers?.count, 3, "应该有3个手势识别器")
        
        // 验证手势识别器类型
        let hasTab = testView.gestureRecognizers?.contains { $0 is UITapGestureRecognizer } ?? false
        let hasPinch = testView.gestureRecognizers?.contains { $0 is UIPinchGestureRecognizer } ?? false
        let hasPan = testView.gestureRecognizers?.contains { $0 is UIPanGestureRecognizer } ?? false
        
        XCTAssertTrue(hasTab, "应该有点击手势识别器")
        XCTAssertTrue(hasPinch, "应该有捏合手势识别器")
        XCTAssertTrue(hasPan, "应该有拖拽手势识别器")
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        print("📱 检测到点击手势")
    }
    
    @objc private func handlePinch(_ gesture: UIPinchGestureRecognizer) {
        print("📱 检测到捏合手势，缩放: \(gesture.scale)")
    }
    
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        let translation = gesture.translation(in: gesture.view)
        print("📱 检测到拖拽手势，位移: \(translation)")
    }
    
    // MARK: - Loading and Progress Tests
    
    func testLoadingIndicators() {
        // 测试加载指示器
        let loadingView = UIView(frame: CGRect(x: 0, y: 0, width: 50, height: 50))
        
        // 测试旋转动画
        AnimationHelper.startRotationAnimation(loadingView)
        
        // 验证动画被添加
        XCTAssertNotNil(loadingView.layer.animation(forKey: "rotation"), "应该有旋转动画")
        
        // 停止动画
        AnimationHelper.stopRotationAnimation(loadingView)
        
        // 验证动画被移除
        XCTAssertNil(loadingView.layer.animation(forKey: "rotation"), "旋转动画应该被移除")
        
        // 测试脉冲动画
        AnimationHelper.startPulseAnimation(loadingView)
        XCTAssertNotNil(loadingView.layer.animation(forKey: "pulse"), "应该有脉冲动画")
        
        AnimationHelper.stopPulseAnimation(loadingView)
        XCTAssertNil(loadingView.layer.animation(forKey: "pulse"), "脉冲动画应该被移除")
    }
    
    func testProgressFeedback() {
        // 测试进度反馈
        let pathSimulation = PathSimulationService()
        
        // 创建测试路径
        let pathPoints = [
            CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
            CLLocationCoordinate2D(latitude: 39.9052, longitude: 116.4084),
            CLLocationCoordinate2D(latitude: 39.9062, longitude: 116.4094)
        ]
        
        // 验证初始进度
        XCTAssertEqual(pathSimulation.progress, 0.0, "初始进度应该为0")
        
        // 模拟进度更新
        pathSimulation.seekToProgress(0.5)
        XCTAssertEqual(pathSimulation.progress, 0.5, accuracy: 0.1, "进度应该更新到0.5")
        
        pathSimulation.seekToProgress(1.0)
        XCTAssertEqual(pathSimulation.progress, 1.0, accuracy: 0.1, "进度应该更新到1.0")
    }
    
    // MARK: - Performance Perception Tests
    
    func testPerceivedPerformance() {
        // 测试感知性能
        let startTime = CFAbsoluteTimeGetCurrent()
        
        // 模拟数据加载
        let locations = (0..<100).map { i in
            VirtualLocationModel(
                name: "感知性能测试\(i)",
                coordinate: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
            )
        }
        
        let loadTime = CFAbsoluteTimeGetCurrent() - startTime
        
        print("📱 数据加载感知时间: \(String(format: "%.3f秒", loadTime))")
        
        // 验证感知性能在可接受范围内
        XCTAssertLessThan(loadTime, 1.0, "数据加载感知时间应该小于1秒")
        
        // 验证数据完整性
        XCTAssertEqual(locations.count, 100, "应该加载100个位置")
    }
    
    func testUserInteractionFeedback() {
        // 测试用户交互反馈
        let button = UIButton(type: .system)
        button.setTitle("测试按钮", for: .normal)
        
        var feedbackReceived = false
        let expectation = XCTestExpectation(description: "用户交互反馈测试")
        
        // 模拟用户交互
        button.addTarget(self, action: #selector(interactionFeedback), for: .touchUpInside)
        
        DispatchQueue.main.async {
            // 模拟按钮点击
            button.sendActions(for: .touchUpInside)
            
            // 添加视觉反馈
            AnimationHelper.animateButtonTap(button) {
                feedbackReceived = true
                expectation.fulfill()
            }
            
            // 添加触觉反馈
            HapticFeedbackHelper.lightImpact()
        }
        
        wait(for: [expectation], timeout: 1.0)
        
        XCTAssertTrue(feedbackReceived, "应该收到用户交互反馈")
    }
    
    @objc private func interactionFeedback() {
        print("📱 用户交互反馈触发")
    }
    
    // MARK: - Error Recovery Tests
    
    func testErrorRecoveryExperience() {
        // 测试错误恢复体验
        let errorHandler = ErrorHandlingService.shared
        
        // 清理错误日志
        errorHandler.clearErrorLog()
        
        // 模拟可恢复的错误
        let recoverableError = LocationError.simulationFailed
        errorHandler.handleError(recoverableError, context: "错误恢复测试", showToUser: false)
        
        // 验证错误被记录
        let errorLog = errorHandler.getErrorLog()
        XCTAssertEqual(errorLog.count, 1, "错误应该被记录")
        
        // 验证错误描述的用户友好性
        let errorDescription = recoverableError.localizedDescription
        XCTAssertFalse(errorDescription.isEmpty, "错误描述应该非空")
        
        print("📱 错误恢复描述: \(errorDescription)")
        
        // 模拟错误恢复
        errorHandler.clearErrorLog()
        
        // 验证恢复后状态
        let clearedLog = errorHandler.getErrorLog()
        XCTAssertEqual(clearedLog.count, 0, "错误日志应该被清空")
    }
}
