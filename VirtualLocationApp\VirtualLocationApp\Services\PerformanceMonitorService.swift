//
//  PerformanceMonitorService.swift
//  VirtualLocationApp
//
//  Created by Dev<PERSON><PERSON> on 2025-06-20.
//  Copyright © 2025 YourCompany. All rights reserved.
//

import Foundation
import UIKit
import os.log

// MARK: - Performance Monitor Service

class PerformanceMonitorService {
    
    // MARK: - Singleton
    
    static let shared = PerformanceMonitorService()
    
    // MARK: - Properties
    
    private var isMonitoring = false
    private var monitoringTimer: Timer?
    private var performanceMetrics: [PerformanceMetric] = []
    private let maxMetricsCount = 1000
    
    // Memory monitoring
    private var lastMemoryUsage: UInt64 = 0
    private var peakMemoryUsage: UInt64 = 0
    
    // CPU monitoring
    private var lastCPUUsage: Double = 0
    private var peakCPUUsage: Double = 0
    
    // Battery monitoring
    private var batteryMonitoringEnabled = false
    
    // App lifecycle
    private var appLaunchTime: Date?
    private var appBecomeActiveTime: Date?
    
    // MARK: - Initialization
    
    private init() {
        setupBatteryMonitoring()
        setupAppLifecycleMonitoring()
    }
    
    // MARK: - Public Methods
    
    /// 开始性能监控
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        appLaunchTime = Date()
        
        // 启动定时监控
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.collectPerformanceMetrics()
        }
        
        print("✅ 性能监控已启动")
    }
    
    /// 停止性能监控
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        monitoringTimer?.invalidate()
        monitoringTimer = nil
        
        print("✅ 性能监控已停止")
    }
    
    /// 获取性能报告
    func getPerformanceReport() -> PerformanceReport {
        let currentMetrics = getCurrentMetrics()
        
        return PerformanceReport(
            memoryUsage: currentMetrics.memoryUsage,
            peakMemoryUsage: peakMemoryUsage,
            cpuUsage: currentMetrics.cpuUsage,
            peakCPUUsage: peakCPUUsage,
            batteryLevel: currentMetrics.batteryLevel,
            appLaunchTime: appLaunchTime,
            totalMetricsCount: performanceMetrics.count,
            averageMemoryUsage: calculateAverageMemoryUsage(),
            averageCPUUsage: calculateAverageCPUUsage()
        )
    }
    
    /// 获取内存使用情况
    func getCurrentMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return info.resident_size
        } else {
            return 0
        }
    }
    
    /// 获取CPU使用率
    func getCurrentCPUUsage() -> Double {
        var info = processor_info_array_t.allocate(capacity: 1)
        var numCpuInfo: mach_msg_type_number_t = 0
        var numCpus: natural_t = 0
        
        let result = host_processor_info(mach_host_self(),
                                       PROCESSOR_CPU_LOAD_INFO,
                                       &numCpus,
                                       &info,
                                       &numCpuInfo)
        
        guard result == KERN_SUCCESS else { return 0.0 }
        
        var totalTicks: UInt64 = 0
        var idleTicks: UInt64 = 0
        
        for i in 0..<Int(numCpus) {
            let cpuInfo = info.advanced(by: Int(CPU_STATE_MAX) * i)
            
            for j in 0..<Int(CPU_STATE_MAX) {
                totalTicks += UInt64(cpuInfo[j])
                if j == Int(CPU_STATE_IDLE) {
                    idleTicks += UInt64(cpuInfo[j])
                }
            }
        }
        
        let cpuUsage = Double(totalTicks - idleTicks) / Double(totalTicks) * 100.0
        
        info.deallocate()
        
        return cpuUsage
    }
    
    /// 记录自定义性能事件
    func recordEvent(_ event: String, duration: TimeInterval? = nil) {
        let metric = PerformanceMetric(
            timestamp: Date(),
            memoryUsage: getCurrentMemoryUsage(),
            cpuUsage: getCurrentCPUUsage(),
            batteryLevel: UIDevice.current.batteryLevel,
            customEvent: event,
            eventDuration: duration
        )
        
        addMetric(metric)
        print("📊 性能事件记录: \(event)")
    }
    
    /// 测量代码块执行时间
    func measureExecutionTime<T>(operation: () throws -> T) rethrows -> (result: T, duration: TimeInterval) {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = try operation()
        let duration = CFAbsoluteTimeGetCurrent() - startTime
        
        recordEvent("代码块执行", duration: duration)
        
        return (result, duration)
    }
    
    /// 检查内存泄漏
    func checkMemoryLeaks() -> MemoryLeakReport {
        let currentMemory = getCurrentMemoryUsage()
        let memoryGrowth = currentMemory > lastMemoryUsage ? currentMemory - lastMemoryUsage : 0
        
        let isLeakSuspected = memoryGrowth > 50 * 1024 * 1024 // 50MB增长视为可疑
        
        return MemoryLeakReport(
            currentMemoryUsage: currentMemory,
            memoryGrowth: memoryGrowth,
            isLeakSuspected: isLeakSuspected,
            timestamp: Date()
        )
    }
    
    /// 导出性能数据
    func exportPerformanceData() -> String {
        let report = getPerformanceReport()
        
        var exportString = "虚拟定位应用性能报告\n"
        exportString += "生成时间: \(Date())\n\n"
        
        exportString += "内存使用情况:\n"
        exportString += "当前内存: \(formatBytes(report.memoryUsage))\n"
        exportString += "峰值内存: \(formatBytes(report.peakMemoryUsage))\n"
        exportString += "平均内存: \(formatBytes(report.averageMemoryUsage))\n\n"
        
        exportString += "CPU使用情况:\n"
        exportString += "当前CPU: \(String(format: "%.2f%%", report.cpuUsage))\n"
        exportString += "峰值CPU: \(String(format: "%.2f%%", report.peakCPUUsage))\n"
        exportString += "平均CPU: \(String(format: "%.2f%%", report.averageCPUUsage))\n\n"
        
        exportString += "电池信息:\n"
        exportString += "当前电量: \(String(format: "%.1f%%", report.batteryLevel * 100))\n\n"
        
        if let launchTime = report.appLaunchTime {
            exportString += "应用启动时间: \(launchTime)\n"
            exportString += "运行时长: \(String(format: "%.2f秒", Date().timeIntervalSince(launchTime)))\n\n"
        }
        
        exportString += "监控数据点: \(report.totalMetricsCount)个\n"
        
        return exportString
    }
    
    // MARK: - Private Methods
    
    private func setupBatteryMonitoring() {
        UIDevice.current.isBatteryMonitoringEnabled = true
        batteryMonitoringEnabled = true
    }
    
    private func setupAppLifecycleMonitoring() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidBecomeActive),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillResignActive),
            name: UIApplication.willResignActiveNotification,
            object: nil
        )
    }
    
    @objc private func appDidBecomeActive() {
        appBecomeActiveTime = Date()
        recordEvent("应用变为活跃状态")
    }
    
    @objc private func appWillResignActive() {
        recordEvent("应用即将变为非活跃状态")
    }
    
    private func collectPerformanceMetrics() {
        let metric = getCurrentMetrics()
        addMetric(metric)
        
        // 更新峰值
        if metric.memoryUsage > peakMemoryUsage {
            peakMemoryUsage = metric.memoryUsage
        }
        
        if metric.cpuUsage > peakCPUUsage {
            peakCPUUsage = metric.cpuUsage
        }
        
        lastMemoryUsage = metric.memoryUsage
        lastCPUUsage = metric.cpuUsage
    }
    
    private func getCurrentMetrics() -> PerformanceMetric {
        return PerformanceMetric(
            timestamp: Date(),
            memoryUsage: getCurrentMemoryUsage(),
            cpuUsage: getCurrentCPUUsage(),
            batteryLevel: UIDevice.current.batteryLevel
        )
    }
    
    private func addMetric(_ metric: PerformanceMetric) {
        performanceMetrics.append(metric)
        
        // 限制数据点数量
        if performanceMetrics.count > maxMetricsCount {
            performanceMetrics.removeFirst()
        }
    }
    
    private func calculateAverageMemoryUsage() -> UInt64 {
        guard !performanceMetrics.isEmpty else { return 0 }
        
        let total = performanceMetrics.reduce(0) { $0 + $1.memoryUsage }
        return total / UInt64(performanceMetrics.count)
    }
    
    private func calculateAverageCPUUsage() -> Double {
        guard !performanceMetrics.isEmpty else { return 0 }
        
        let total = performanceMetrics.reduce(0) { $0 + $1.cpuUsage }
        return total / Double(performanceMetrics.count)
    }
    
    private func formatBytes(_ bytes: UInt64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB, .useGB]
        formatter.countStyle = .memory
        return formatter.string(fromByteCount: Int64(bytes))
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - Performance Models

struct PerformanceMetric {
    let timestamp: Date
    let memoryUsage: UInt64
    let cpuUsage: Double
    let batteryLevel: Float
    let customEvent: String?
    let eventDuration: TimeInterval?
    
    init(timestamp: Date, memoryUsage: UInt64, cpuUsage: Double, batteryLevel: Float, customEvent: String? = nil, eventDuration: TimeInterval? = nil) {
        self.timestamp = timestamp
        self.memoryUsage = memoryUsage
        self.cpuUsage = cpuUsage
        self.batteryLevel = batteryLevel
        self.customEvent = customEvent
        self.eventDuration = eventDuration
    }
}

struct PerformanceReport {
    let memoryUsage: UInt64
    let peakMemoryUsage: UInt64
    let cpuUsage: Double
    let peakCPUUsage: Double
    let batteryLevel: Float
    let appLaunchTime: Date?
    let totalMetricsCount: Int
    let averageMemoryUsage: UInt64
    let averageCPUUsage: Double
}

struct MemoryLeakReport {
    let currentMemoryUsage: UInt64
    let memoryGrowth: UInt64
    let isLeakSuspected: Bool
    let timestamp: Date
}
