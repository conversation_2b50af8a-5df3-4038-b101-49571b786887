//
//  PerformanceTests.swift
//  VirtualLocationAppTests
//
//  Created by <PERSON><PERSON><PERSON> on 2025-06-20.
//  Copyright © 2025 YourCompany. All rights reserved.
//

import XCTest
import CoreLocation
@testable import VirtualLocationApp

class PerformanceTests: XCTestCase {
    
    // MARK: - Test Properties
    
    var performanceMonitor: PerformanceMonitorService!
    var locationService: LocationService!
    var dataService: DataService!
    var pathSimulationService: PathSimulationService!
    
    // MARK: - Setup and Teardown
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        
        performanceMonitor = PerformanceMonitorService.shared
        locationService = LocationService.shared
        dataService = DataService.shared
        pathSimulationService = PathSimulationService()
        
        // 启动性能监控
        performanceMonitor.startMonitoring()
        
        // 清理测试数据
        dataService.clearAllData()
    }
    
    override func tearDownWithError() throws {
        // 停止所有服务
        locationService.stopVirtualLocation()
        pathSimulationService.stopPathSimulation()
        performanceMonitor.stopMonitoring()
        
        // 清理测试数据
        dataService.clearAllData()
        
        try super.tearDownWithError()
    }
    
    // MARK: - Memory Performance Tests
    
    func testMemoryUsageBaseline() {
        // 测试基线内存使用
        let initialMemory = performanceMonitor.getCurrentMemoryUsage()
        
        // 执行基本操作
        let coordinate = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
        let location = VirtualLocationModel(name: "内存测试", coordinate: coordinate)
        dataService.saveLocationToHistory(location)
        
        let finalMemory = performanceMonitor.getCurrentMemoryUsage()
        let memoryGrowth = finalMemory > initialMemory ? finalMemory - initialMemory : 0
        
        // 验证内存增长在合理范围内（小于10MB）
        XCTAssertLessThan(memoryGrowth, 10 * 1024 * 1024, "基本操作的内存增长应该小于10MB")
        
        print("📊 基线内存使用: \(initialMemory / 1024 / 1024)MB -> \(finalMemory / 1024 / 1024)MB")
    }
    
    func testMemoryLeakDetection() {
        // 测试内存泄漏检测
        let initialMemory = performanceMonitor.getCurrentMemoryUsage()
        
        // 执行大量操作
        for i in 0..<1000 {
            let coordinate = CLLocationCoordinate2D(
                latitude: 39.9042 + Double(i) * 0.0001,
                longitude: 116.4074 + Double(i) * 0.0001
            )
            let location = VirtualLocationModel(name: "测试位置\(i)", coordinate: coordinate)
            dataService.saveLocationToHistory(location)
        }
        
        // 清理数据
        dataService.clearLocationHistory()
        
        // 强制垃圾回收
        autoreleasepool {
            // 空的自动释放池，帮助释放临时对象
        }
        
        let finalMemory = performanceMonitor.getCurrentMemoryUsage()
        let memoryGrowth = finalMemory > initialMemory ? finalMemory - initialMemory : 0
        
        // 检查内存泄漏
        let leakReport = performanceMonitor.checkMemoryLeaks()
        
        print("📊 内存泄漏检测: 增长\(memoryGrowth / 1024 / 1024)MB, 可疑: \(leakReport.isLeakSuspected)")
        
        // 验证没有严重的内存泄漏（增长小于50MB）
        XCTAssertLessThan(memoryGrowth, 50 * 1024 * 1024, "内存增长应该小于50MB")
    }
    
    func testVirtualLocationMemoryUsage() {
        // 测试虚拟定位的内存使用
        let initialMemory = performanceMonitor.getCurrentMemoryUsage()
        
        measure(metrics: [XCTMemoryMetric()]) {
            let coordinate = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
            locationService.startVirtualLocation(at: coordinate)
            
            // 运行一段时间
            RunLoop.current.run(until: Date().addingTimeInterval(2.0))
            
            locationService.stopVirtualLocation()
        }
        
        let finalMemory = performanceMonitor.getCurrentMemoryUsage()
        print("📊 虚拟定位内存使用: \(initialMemory / 1024 / 1024)MB -> \(finalMemory / 1024 / 1024)MB")
    }
    
    // MARK: - CPU Performance Tests
    
    func testCPUUsageBaseline() {
        // 测试基线CPU使用
        let initialCPU = performanceMonitor.getCurrentCPUUsage()
        
        // 执行基本操作
        for i in 0..<100 {
            let coordinate = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
            _ = VirtualLocationModel(name: "CPU测试\(i)", coordinate: coordinate)
        }
        
        let finalCPU = performanceMonitor.getCurrentCPUUsage()
        
        print("📊 基线CPU使用: \(String(format: "%.2f%%", initialCPU)) -> \(String(format: "%.2f%%", finalCPU))")
        
        // 验证CPU使用率在合理范围内
        XCTAssertLessThan(finalCPU, 80.0, "基本操作的CPU使用率应该小于80%")
    }
    
    func testHighFrequencyLocationUpdates() {
        // 测试高频率位置更新的性能
        measure(metrics: [XCTCPUMetric(), XCTMemoryMetric()]) {
            let coordinate = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
            
            // 设置高频率更新
            locationService.setLocationUpdateInterval(0.1) // 100ms
            locationService.startVirtualLocation(at: coordinate)
            
            // 运行3秒
            RunLoop.current.run(until: Date().addingTimeInterval(3.0))
            
            locationService.stopVirtualLocation()
        }
    }
    
    func testPathSimulationPerformance() {
        // 测试路径模拟性能
        let pathPoints = (0..<100).map { i in
            CLLocationCoordinate2D(
                latitude: 39.9042 + Double(i) * 0.0001,
                longitude: 116.4074 + Double(i) * 0.0001
            )
        }
        
        measure(metrics: [XCTCPUMetric(), XCTMemoryMetric()]) {
            pathSimulationService.startPathSimulation(path: pathPoints, speed: 10.0)
            
            // 运行路径模拟
            RunLoop.current.run(until: Date().addingTimeInterval(5.0))
            
            pathSimulationService.stopPathSimulation()
        }
    }
    
    // MARK: - Data Performance Tests
    
    func testDataServicePerformance() {
        // 测试数据服务性能
        let locations = (0..<1000).map { i in
            VirtualLocationModel(
                name: "性能测试位置\(i)",
                coordinate: CLLocationCoordinate2D(
                    latitude: 39.9042 + Double(i) * 0.0001,
                    longitude: 116.4074 + Double(i) * 0.0001
                )
            )
        }
        
        measure(metrics: [XCTStorageMetric(), XCTMemoryMetric()]) {
            // 批量保存位置
            for location in locations {
                dataService.saveLocationToHistory(location)
            }
            
            // 批量读取位置
            _ = dataService.getLocationHistory()
            
            // 清理数据
            dataService.clearLocationHistory()
        }
    }
    
    func testLargeDataSetHandling() {
        // 测试大数据集处理
        let largeDataSet = (0..<5000).map { i in
            VirtualLocationModel(
                name: "大数据集测试\(i)",
                coordinate: CLLocationCoordinate2D(
                    latitude: 39.9042 + Double(i) * 0.0001,
                    longitude: 116.4074 + Double(i) * 0.0001
                )
            )
        }
        
        let (_, duration) = performanceMonitor.measureExecutionTime {
            for location in largeDataSet {
                dataService.saveLocationToHistory(location)
            }
        }
        
        print("📊 大数据集处理时间: \(String(format: "%.3f秒", duration))")
        
        // 验证处理时间在合理范围内
        XCTAssertLessThan(duration, 10.0, "处理5000条数据应该在10秒内完成")
        
        // 清理数据
        dataService.clearLocationHistory()
    }
    
    // MARK: - Coordinate Calculation Performance Tests
    
    func testCoordinateCalculationPerformance() {
        // 测试坐标计算性能
        let coordinates = (0..<10000).map { i in
            CLLocationCoordinate2D(
                latitude: 39.9042 + Double(i) * 0.0001,
                longitude: 116.4074 + Double(i) * 0.0001
            )
        }
        
        measure {
            for i in 0..<coordinates.count-1 {
                let coord1 = coordinates[i]
                let coord2 = coordinates[i+1]
                
                // 测试距离计算
                _ = coord1.distance(to: coord2)
                
                // 测试方位角计算
                _ = coord1.bearing(to: coord2)
                
                // 测试坐标验证
                _ = coord1.isValid
            }
        }
    }
    
    func testCoordinateConversionPerformance() {
        // 测试坐标转换性能
        let coordinates = (0..<1000).map { i in
            CLLocationCoordinate2D(
                latitude: 39.9042 + Double(i) * 0.001,
                longitude: 116.4074 + Double(i) * 0.001
            )
        }
        
        measure {
            for coordinate in coordinates {
                // 测试坐标系转换
                _ = coordinate.toGCJ02
                _ = coordinate.toBD09
                _ = coordinate.toWGS84
            }
        }
    }
    
    // MARK: - Error Handling Performance Tests
    
    func testErrorHandlingPerformance() {
        // 测试错误处理性能
        let errorHandler = ErrorHandlingService.shared
        
        measure {
            for i in 0..<1000 {
                let error = LocationError.invalidCoordinate
                errorHandler.handleError(error, context: "性能测试\(i)", showToUser: false)
            }
        }
        
        // 验证错误日志数量
        let errorLog = errorHandler.getErrorLog()
        XCTAssertGreaterThan(errorLog.count, 0, "应该有错误日志记录")
        
        // 清理错误日志
        errorHandler.clearErrorLog()
    }
    
    // MARK: - App Launch Performance Tests
    
    func testAppLaunchTime() {
        // 测试应用启动时间（模拟）
        let (_, launchDuration) = performanceMonitor.measureExecutionTime {
            // 模拟应用启动过程
            _ = LocationService.shared
            _ = DataService.shared
            _ = ErrorHandlingService.shared
            
            // 模拟UI初始化
            Thread.sleep(forTimeInterval: 0.1)
        }
        
        print("📊 模拟应用启动时间: \(String(format: "%.3f秒", launchDuration))")
        
        // 验证启动时间在合理范围内
        XCTAssertLessThan(launchDuration, 2.0, "应用启动时间应该小于2秒")
    }
    
    // MARK: - Battery Performance Tests
    
    func testBatteryImpact() {
        // 测试电池影响（模拟）
        let initialBatteryLevel = UIDevice.current.batteryLevel
        
        // 执行耗电操作
        let coordinate = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
        locationService.setLocationUpdateInterval(0.1) // 高频率更新
        locationService.startVirtualLocation(at: coordinate)
        
        // 运行一段时间
        RunLoop.current.run(until: Date().addingTimeInterval(5.0))
        
        locationService.stopVirtualLocation()
        
        let finalBatteryLevel = UIDevice.current.batteryLevel
        
        print("📊 电池影响测试: \(String(format: "%.1f%%", initialBatteryLevel * 100)) -> \(String(format: "%.1f%%", finalBatteryLevel * 100))")
        
        // 注意：在模拟器中电池电量可能不会变化
        if initialBatteryLevel > 0 && finalBatteryLevel > 0 {
            let batteryDrop = initialBatteryLevel - finalBatteryLevel
            XCTAssertLessThan(batteryDrop, 0.05, "5秒内电池消耗应该小于5%")
        }
    }
    
    // MARK: - Stress Tests
    
    func testStressTest() {
        // 压力测试
        let stressTestDuration: TimeInterval = 10.0
        let startTime = Date()
        
        var operationCount = 0
        
        while Date().timeIntervalSince(startTime) < stressTestDuration {
            // 执行各种操作
            let coordinate = CLLocationCoordinate2D(
                latitude: 39.9042 + Double.random(in: -0.01...0.01),
                longitude: 116.4074 + Double.random(in: -0.01...0.01)
            )
            
            let location = VirtualLocationModel(name: "压力测试\(operationCount)", coordinate: coordinate)
            dataService.saveLocationToHistory(location)
            
            if operationCount % 100 == 0 {
                _ = dataService.getLocationHistory()
            }
            
            operationCount += 1
        }
        
        print("📊 压力测试完成: \(operationCount)次操作在\(stressTestDuration)秒内")
        
        // 验证应用仍然响应
        let finalHistory = dataService.getLocationHistory()
        XCTAssertGreaterThan(finalHistory.count, 0, "压力测试后应用应该仍然正常工作")
        
        // 清理数据
        dataService.clearLocationHistory()
    }
    
    // MARK: - Performance Report Tests
    
    func testPerformanceReportGeneration() {
        // 测试性能报告生成
        let (report, duration) = performanceMonitor.measureExecutionTime {
            return performanceMonitor.getPerformanceReport()
        }
        
        print("📊 性能报告生成时间: \(String(format: "%.3f秒", duration))")
        
        // 验证报告内容
        XCTAssertGreaterThan(report.memoryUsage, 0, "内存使用应该大于0")
        XCTAssertGreaterThanOrEqual(report.cpuUsage, 0, "CPU使用应该大于等于0")
        XCTAssertGreaterThanOrEqual(report.batteryLevel, 0, "电池电量应该大于等于0")
        
        // 验证报告生成时间
        XCTAssertLessThan(duration, 1.0, "性能报告生成应该在1秒内完成")
    }
}
