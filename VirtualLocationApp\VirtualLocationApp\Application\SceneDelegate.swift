//
//  SceneDelegate.swift
//  VirtualLocationApp
//
//  Created by <PERSON><PERSON><PERSON> on 2025-06-20.
//  Copyright © 2025 YourCompany. All rights reserved.
//

import UIKit

class SceneDelegate: UIResponder, UIWindowSceneDelegate {

    var window: UIWindow?

    func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
        // Use this method to optionally configure and attach the UIWindow `window` to the provided UIWindowScene `scene`.
        // If using a storyboard, the `window` property will automatically be set and attached to the scene.
        // This delegate does not imply the connecting scene or session are new (see `application:configurationForConnectingSceneSession` instead).
        
        guard let windowScene = (scene as? UIWindowScene) else { return }
        
        // 创建窗口
        window = UIWindow(windowScene: windowScene)
        
        // 设置根视图控制器
        setupRootViewController()
        
        // 显示窗口
        window?.makeKeyAndVisible()
    }

    func sceneDidDisconnect(_ scene: UIScene) {
        // Called as the scene is being released by the system.
        // This occurs shortly after the scene enters the background, or when its session is discarded.
        // Release any resources associated with this scene that can be re-created the next time the scene connects.
        // The scene may re-connect later, as its session was not necessarily discarded (see `application:didDiscardSceneSessions` instead).
    }

    func sceneDidBecomeActive(_ scene: UIScene) {
        // Called when the scene has moved from an inactive state to an active state.
        // Use this method to restart any tasks that were paused (or not yet started) when the scene was inactive.
        
        // 应用变为活跃状态时的处理
        NotificationCenter.default.post(name: .appDidBecomeActive, object: nil)
    }

    func sceneWillResignActive(_ scene: UIScene) {
        // Called when the scene will move from an active state to an inactive state.
        // This may occur due to temporary interruptions (ex. an incoming phone call).
        
        // 应用即将变为非活跃状态时的处理
        NotificationCenter.default.post(name: .appWillResignActive, object: nil)
    }

    func sceneWillEnterForeground(_ scene: UIScene) {
        // Called as the scene transitions from the background to the foreground.
        // Use this method to undo the changes made on entering the background.
        
        // 应用即将进入前台时的处理
        NotificationCenter.default.post(name: .appWillEnterForeground, object: nil)
    }

    func sceneDidEnterBackground(_ scene: UIScene) {
        // Called as the scene transitions from the foreground to the background.
        // Use this method to save data, release shared resources, and store enough scene-specific state information
        // to restore the scene back to its current state.
        
        // 应用进入后台时的处理
        NotificationCenter.default.post(name: .appDidEnterBackground, object: nil)
        
        // 保存数据
        DataService.shared.saveData()
    }
    
    // MARK: - Private Methods
    
    private func setupRootViewController() {
        // 创建主视图控制器
        let mainViewController = MainViewController()
        let navigationController = UINavigationController(rootViewController: mainViewController)
        
        // 设置导航栏样式
        navigationController.navigationBar.prefersLargeTitles = false
        navigationController.navigationBar.tintColor = UIColor.white
        
        // 设置为根视图控制器
        window?.rootViewController = navigationController
    }
}

// MARK: - Notification Names Extension

extension Notification.Name {
    static let appDidBecomeActive = Notification.Name("AppDidBecomeActive")
    static let appWillResignActive = Notification.Name("AppWillResignActive")
    static let appWillEnterForeground = Notification.Name("AppWillEnterForeground")
    static let appDidEnterBackground = Notification.Name("AppDidEnterBackground")
}
