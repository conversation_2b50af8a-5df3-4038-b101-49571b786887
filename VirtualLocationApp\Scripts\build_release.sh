#!/bin/bash

# 虚拟定位应用发布构建脚本
# 用于自动化构建和打包发布版本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="VirtualLocationApp"
SCHEME_NAME="VirtualLocationApp"
WORKSPACE_PATH="VirtualLocationApp.xcworkspace"
PROJECT_PATH="VirtualLocationApp.xcodeproj"
CONFIGURATION="Release"
ARCHIVE_PATH="./build/VirtualLocationApp.xcarchive"
EXPORT_PATH="./build/export"
IPA_PATH="./build/VirtualLocationApp.ipa"

# 版本信息
VERSION_NUMBER="1.0.0"
BUILD_NUMBER=$(date +%Y%m%d%H%M)

echo -e "${BLUE}🚀 开始构建虚拟定位应用发布版本${NC}"
echo -e "${BLUE}版本号: ${VERSION_NUMBER}${NC}"
echo -e "${BLUE}构建号: ${BUILD_NUMBER}${NC}"

# 检查Xcode是否安装
check_xcode() {
    echo -e "${YELLOW}📋 检查Xcode环境...${NC}"
    
    if ! command -v xcodebuild &> /dev/null; then
        echo -e "${RED}❌ 错误: 未找到Xcode命令行工具${NC}"
        echo -e "${RED}请安装Xcode并运行: xcode-select --install${NC}"
        exit 1
    fi
    
    XCODE_VERSION=$(xcodebuild -version | head -n 1)
    echo -e "${GREEN}✅ Xcode环境检查通过: ${XCODE_VERSION}${NC}"
}

# 清理构建目录
clean_build() {
    echo -e "${YELLOW}🧹 清理构建目录...${NC}"
    
    rm -rf ./build
    mkdir -p ./build
    
    echo -e "${GREEN}✅ 构建目录清理完成${NC}"
}

# 更新版本号
update_version() {
    echo -e "${YELLOW}📝 更新版本信息...${NC}"
    
    # 更新Info.plist中的版本号
    /usr/libexec/PlistBuddy -c "Set :CFBundleShortVersionString ${VERSION_NUMBER}" "${PROJECT_NAME}/Info.plist"
    /usr/libexec/PlistBuddy -c "Set :CFBundleVersion ${BUILD_NUMBER}" "${PROJECT_NAME}/Info.plist"
    
    echo -e "${GREEN}✅ 版本信息更新完成${NC}"
}

# 运行单元测试
run_tests() {
    echo -e "${YELLOW}🧪 运行单元测试...${NC}"
    
    if [ -f "$WORKSPACE_PATH" ]; then
        xcodebuild test \
            -workspace "$WORKSPACE_PATH" \
            -scheme "$SCHEME_NAME" \
            -destination 'platform=iOS Simulator,name=iPhone 14,OS=latest' \
            -configuration Debug \
            -quiet
    else
        xcodebuild test \
            -project "$PROJECT_PATH" \
            -scheme "$SCHEME_NAME" \
            -destination 'platform=iOS Simulator,name=iPhone 14,OS=latest' \
            -configuration Debug \
            -quiet
    fi
    
    echo -e "${GREEN}✅ 单元测试通过${NC}"
}

# 代码质量检查
code_quality_check() {
    echo -e "${YELLOW}🔍 执行代码质量检查...${NC}"
    
    # SwiftLint检查（如果安装了）
    if command -v swiftlint &> /dev/null; then
        echo -e "${BLUE}运行SwiftLint检查...${NC}"
        swiftlint --strict
        echo -e "${GREEN}✅ SwiftLint检查通过${NC}"
    else
        echo -e "${YELLOW}⚠️  SwiftLint未安装，跳过代码风格检查${NC}"
    fi
    
    # 检查TODO和FIXME
    echo -e "${BLUE}检查代码中的TODO和FIXME...${NC}"
    TODO_COUNT=$(find . -name "*.swift" -exec grep -l "TODO\|FIXME" {} \; | wc -l)
    if [ $TODO_COUNT -gt 0 ]; then
        echo -e "${YELLOW}⚠️  发现 ${TODO_COUNT} 个文件包含TODO或FIXME${NC}"
        find . -name "*.swift" -exec grep -Hn "TODO\|FIXME" {} \;
    else
        echo -e "${GREEN}✅ 未发现待处理的TODO或FIXME${NC}"
    fi
}

# 构建Archive
build_archive() {
    echo -e "${YELLOW}🔨 构建Archive...${NC}"
    
    if [ -f "$WORKSPACE_PATH" ]; then
        xcodebuild archive \
            -workspace "$WORKSPACE_PATH" \
            -scheme "$SCHEME_NAME" \
            -configuration "$CONFIGURATION" \
            -archivePath "$ARCHIVE_PATH" \
            -quiet \
            CODE_SIGN_STYLE=Automatic \
            DEVELOPMENT_TEAM="YOUR_TEAM_ID"
    else
        xcodebuild archive \
            -project "$PROJECT_PATH" \
            -scheme "$SCHEME_NAME" \
            -configuration "$CONFIGURATION" \
            -archivePath "$ARCHIVE_PATH" \
            -quiet \
            CODE_SIGN_STYLE=Automatic \
            DEVELOPMENT_TEAM="YOUR_TEAM_ID"
    fi
    
    echo -e "${GREEN}✅ Archive构建完成${NC}"
}

# 导出IPA
export_ipa() {
    echo -e "${YELLOW}📦 导出IPA文件...${NC}"
    
    # 创建ExportOptions.plist
    cat > ./build/ExportOptions.plist << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>method</key>
    <string>app-store</string>
    <key>teamID</key>
    <string>YOUR_TEAM_ID</string>
    <key>uploadBitcode</key>
    <false/>
    <key>uploadSymbols</key>
    <true/>
    <key>compileBitcode</key>
    <false/>
</dict>
</plist>
EOF
    
    xcodebuild -exportArchive \
        -archivePath "$ARCHIVE_PATH" \
        -exportPath "$EXPORT_PATH" \
        -exportOptionsPlist ./build/ExportOptions.plist \
        -quiet
    
    # 移动IPA文件到指定位置
    mv "$EXPORT_PATH"/*.ipa "$IPA_PATH"
    
    echo -e "${GREEN}✅ IPA文件导出完成: ${IPA_PATH}${NC}"
}

# 验证IPA
validate_ipa() {
    echo -e "${YELLOW}🔍 验证IPA文件...${NC}"
    
    # 检查IPA文件大小
    IPA_SIZE=$(du -h "$IPA_PATH" | cut -f1)
    echo -e "${BLUE}IPA文件大小: ${IPA_SIZE}${NC}"
    
    # 检查IPA内容
    unzip -l "$IPA_PATH" | head -20
    
    echo -e "${GREEN}✅ IPA文件验证完成${NC}"
}

# 生成构建报告
generate_report() {
    echo -e "${YELLOW}📊 生成构建报告...${NC}"
    
    REPORT_FILE="./build/build_report.txt"
    
    cat > "$REPORT_FILE" << EOF
虚拟定位应用构建报告
===================

构建信息:
- 项目名称: ${PROJECT_NAME}
- 版本号: ${VERSION_NUMBER}
- 构建号: ${BUILD_NUMBER}
- 构建配置: ${CONFIGURATION}
- 构建时间: $(date)
- 构建环境: $(uname -a)
- Xcode版本: $(xcodebuild -version | head -n 1)

文件信息:
- Archive路径: ${ARCHIVE_PATH}
- IPA路径: ${IPA_PATH}
- IPA大小: $(du -h "$IPA_PATH" | cut -f1)

构建状态: 成功 ✅

下一步操作:
1. 在App Store Connect中上传IPA
2. 填写应用元数据和截图
3. 提交审核

EOF
    
    echo -e "${GREEN}✅ 构建报告已生成: ${REPORT_FILE}${NC}"
    cat "$REPORT_FILE"
}

# 主函数
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}虚拟定位应用发布构建脚本${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    check_xcode
    clean_build
    update_version
    
    # 可选：运行测试（在CI环境中建议启用）
    if [ "$RUN_TESTS" = "true" ]; then
        run_tests
    fi
    
    code_quality_check
    build_archive
    export_ipa
    validate_ipa
    generate_report
    
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}🎉 构建完成！${NC}"
    echo -e "${GREEN}IPA文件位置: ${IPA_PATH}${NC}"
    echo -e "${GREEN}========================================${NC}"
}

# 错误处理
trap 'echo -e "${RED}❌ 构建失败！${NC}"; exit 1' ERR

# 执行主函数
main "$@"
