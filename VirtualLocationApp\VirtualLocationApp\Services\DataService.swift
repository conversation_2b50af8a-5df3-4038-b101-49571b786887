//
//  DataService.swift
//  VirtualLocationApp
//
//  Created by <PERSON><PERSON><PERSON> on 2025-06-20.
//  Copyright © 2025 YourCompany. All rights reserved.
//

import Foundation
import CoreLocation

// MARK: - Data Service

class DataService {
    
    // MARK: - Singleton
    
    static let shared = DataService()
    
    // MARK: - Properties
    
    private let userDefaults = UserDefaults.standard
    private let maxHistoryCount = 50
    
    // UserDefaults Keys
    private enum Keys {
        static let locationHistory = "LocationHistory"
        static let favoriteLocations = "FavoriteLocations"
        static let appConfiguration = "AppConfiguration"
        static let lastUsedLocation = "LastUsedLocation"
        static let savedPaths = "SavedPaths"
        static let pathHistory = "PathHistory"
    }
    
    // MARK: - Initialization
    
    private init() {
        setupDefaultConfiguration()
    }
    
    // MARK: - Location History Management
    
    /// 保存位置到历史记录
    func saveLocationToHistory(_ location: VirtualLocationModel) {
        var history = getLocationHistory()
        
        // 检查是否已存在相同位置
        if let existingIndex = history.firstIndex(where: { $0.id == location.id }) {
            history.remove(at: existingIndex)
        }
        
        // 添加到开头
        history.insert(location, at: 0)
        
        // 限制历史记录数量
        if history.count > maxHistoryCount {
            history = Array(history.prefix(maxHistoryCount))
        }
        
        // 保存到UserDefaults
        if let data = try? JSONEncoder().encode(history) {
            userDefaults.set(data, forKey: Keys.locationHistory)
        }
    }
    
    /// 获取位置历史记录
    func getLocationHistory() -> [VirtualLocationModel] {
        guard let data = userDefaults.data(forKey: Keys.locationHistory),
              let history = try? JSONDecoder().decode([VirtualLocationModel].self, from: data) else {
            return []
        }
        return history
    }
    
    /// 删除历史记录中的位置
    func deleteLocationFromHistory(withId id: UUID) {
        var history = getLocationHistory()
        history.removeAll { $0.id == id }
        
        if let data = try? JSONEncoder().encode(history) {
            userDefaults.set(data, forKey: Keys.locationHistory)
        }
    }
    
    /// 清空历史记录
    func clearLocationHistory() {
        userDefaults.removeObject(forKey: Keys.locationHistory)
    }
    
    // MARK: - Favorite Locations Management
    
    /// 保存收藏位置
    func saveFavoriteLocation(_ location: VirtualLocationModel) {
        var favorites = getFavoriteLocations()
        
        // 检查是否已存在
        if !favorites.contains(where: { $0.id == location.id }) {
            favorites.append(location)
            
            if let data = try? JSONEncoder().encode(favorites) {
                userDefaults.set(data, forKey: Keys.favoriteLocations)
            }
        }
    }
    
    /// 获取收藏位置列表
    func getFavoriteLocations() -> [VirtualLocationModel] {
        guard let data = userDefaults.data(forKey: Keys.favoriteLocations),
              let favorites = try? JSONDecoder().decode([VirtualLocationModel].self, from: data) else {
            return []
        }
        return favorites
    }
    
    /// 删除收藏位置
    func deleteFavoriteLocation(withId id: UUID) {
        var favorites = getFavoriteLocations()
        favorites.removeAll { $0.id == id }
        
        if let data = try? JSONEncoder().encode(favorites) {
            userDefaults.set(data, forKey: Keys.favoriteLocations)
        }
    }
    
    /// 检查位置是否已收藏
    func isLocationFavorited(withId id: UUID) -> Bool {
        return getFavoriteLocations().contains { $0.id == id }
    }
    
    // MARK: - App Configuration Management
    
    /// 保存应用配置
    func saveAppConfiguration(_ config: AppConfiguration) {
        if let data = try? JSONEncoder().encode(config) {
            userDefaults.set(data, forKey: Keys.appConfiguration)
        }
    }
    
    /// 获取应用配置
    func getAppConfiguration() -> AppConfiguration {
        guard let data = userDefaults.data(forKey: Keys.appConfiguration),
              let config = try? JSONDecoder().decode(AppConfiguration.self, from: data) else {
            return AppConfiguration.default
        }
        return config
    }
    
    // MARK: - Last Used Location
    
    /// 保存最后使用的位置
    func saveLastUsedLocation(_ location: VirtualLocationModel) {
        if let data = try? JSONEncoder().encode(location) {
            userDefaults.set(data, forKey: Keys.lastUsedLocation)
        }
    }
    
    /// 获取最后使用的位置
    func getLastUsedLocation() -> VirtualLocationModel? {
        guard let data = userDefaults.data(forKey: Keys.lastUsedLocation),
              let location = try? JSONDecoder().decode(VirtualLocationModel.self, from: data) else {
            return nil
        }
        return location
    }

    // MARK: - Path Management

    /// 保存路径
    func savePath(_ path: PathModel) {
        var savedPaths = getSavedPaths()

        // 检查是否已存在相同路径
        if let existingIndex = savedPaths.firstIndex(where: { $0.id == path.id }) {
            savedPaths[existingIndex] = path
        } else {
            savedPaths.append(path)
        }

        if let data = try? JSONEncoder().encode(savedPaths) {
            userDefaults.set(data, forKey: Keys.savedPaths)
        }
    }

    /// 获取保存的路径列表
    func getSavedPaths() -> [PathModel] {
        guard let data = userDefaults.data(forKey: Keys.savedPaths),
              let paths = try? JSONDecoder().decode([PathModel].self, from: data) else {
            return []
        }
        return paths
    }

    /// 删除保存的路径
    func deleteSavedPath(withId id: UUID) {
        var savedPaths = getSavedPaths()
        savedPaths.removeAll { $0.id == id }

        if let data = try? JSONEncoder().encode(savedPaths) {
            userDefaults.set(data, forKey: Keys.savedPaths)
        }
    }

    /// 保存路径历史记录
    func savePathToHistory(_ pathHistory: PathHistoryModel) {
        var history = getPathHistory()

        // 添加到开头
        history.insert(pathHistory, at: 0)

        // 限制历史记录数量
        if history.count > maxHistoryCount {
            history = Array(history.prefix(maxHistoryCount))
        }

        if let data = try? JSONEncoder().encode(history) {
            userDefaults.set(data, forKey: Keys.pathHistory)
        }
    }

    /// 获取路径历史记录
    func getPathHistory() -> [PathHistoryModel] {
        guard let data = userDefaults.data(forKey: Keys.pathHistory),
              let history = try? JSONDecoder().decode([PathHistoryModel].self, from: data) else {
            return []
        }
        return history
    }

    /// 清空路径历史记录
    func clearPathHistory() {
        userDefaults.removeObject(forKey: Keys.pathHistory)
    }
    
    // MARK: - Data Management
    
    /// 保存所有数据
    func saveData() {
        userDefaults.synchronize()
    }
    
    /// 清空所有数据
    func clearAllData() {
        userDefaults.removeObject(forKey: Keys.locationHistory)
        userDefaults.removeObject(forKey: Keys.favoriteLocations)
        userDefaults.removeObject(forKey: Keys.lastUsedLocation)
        setupDefaultConfiguration()
    }
    
    /// 导出数据
    func exportData() -> [String: Any] {
        return [
            "locationHistory": getLocationHistory(),
            "favoriteLocations": getFavoriteLocations(),
            "appConfiguration": getAppConfiguration(),
            "lastUsedLocation": getLastUsedLocation() as Any
        ]
    }
    
    // MARK: - Private Methods
    
    private func setupDefaultConfiguration() {
        let defaultConfig = AppConfiguration.default
        saveAppConfiguration(defaultConfig)
    }
}

// MARK: - Virtual Location Model

struct VirtualLocationModel: Codable, Identifiable, Equatable {
    let id: UUID
    let name: String
    let latitude: Double
    let longitude: Double
    let accuracy: Double
    let timestamp: Date
    let address: String?
    
    init(id: UUID = UUID(), name: String, coordinate: CLLocationCoordinate2D, accuracy: Double = 5.0, address: String? = nil) {
        self.id = id
        self.name = name
        self.latitude = coordinate.latitude
        self.longitude = coordinate.longitude
        self.accuracy = accuracy
        self.timestamp = Date()
        self.address = address
    }
    
    var coordinate: CLLocationCoordinate2D {
        return CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
    }
    
    var location: CLLocation {
        return CLLocation(
            coordinate: coordinate,
            altitude: 0,
            horizontalAccuracy: accuracy,
            verticalAccuracy: accuracy,
            timestamp: timestamp
        )
    }
    
    static func == (lhs: VirtualLocationModel, rhs: VirtualLocationModel) -> Bool {
        return lhs.id == rhs.id
    }
}

// MARK: - Path Model

struct PathModel: Codable, Identifiable, Equatable {
    let id: UUID
    let name: String
    let points: [CLLocationCoordinate2D]
    let totalDistance: Double
    let estimatedDuration: TimeInterval
    let createdAt: Date
    let description: String?

    init(id: UUID = UUID(), name: String, points: [CLLocationCoordinate2D], description: String? = nil) {
        self.id = id
        self.name = name
        self.points = points
        self.description = description
        self.createdAt = Date()

        // 计算总距离
        var distance: Double = 0
        for i in 0..<(points.count - 1) {
            let location1 = CLLocation(latitude: points[i].latitude, longitude: points[i].longitude)
            let location2 = CLLocation(latitude: points[i + 1].latitude, longitude: points[i + 1].longitude)
            distance += location1.distance(from: location2)
        }
        self.totalDistance = distance

        // 估算持续时间（假设平均速度5m/s）
        self.estimatedDuration = distance / 5.0
    }

    var formattedDistance: String {
        if totalDistance < 1000 {
            return String(format: "%.0f米", totalDistance)
        } else {
            return String(format: "%.2f公里", totalDistance / 1000)
        }
    }

    var formattedDuration: String {
        let minutes = Int(estimatedDuration) / 60
        let seconds = Int(estimatedDuration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    static func == (lhs: PathModel, rhs: PathModel) -> Bool {
        return lhs.id == rhs.id
    }
}

// MARK: - Path History Model

struct PathHistoryModel: Codable, Identifiable {
    let id: UUID
    let pathId: UUID
    let pathName: String
    let startTime: Date
    let endTime: Date
    let actualDistance: Double
    let averageSpeed: Double
    let completionRate: Double // 0.0 - 1.0

    init(pathId: UUID, pathName: String, startTime: Date, endTime: Date, actualDistance: Double, averageSpeed: Double, completionRate: Double) {
        self.id = UUID()
        self.pathId = pathId
        self.pathName = pathName
        self.startTime = startTime
        self.endTime = endTime
        self.actualDistance = actualDistance
        self.averageSpeed = averageSpeed
        self.completionRate = completionRate
    }

    var duration: TimeInterval {
        return endTime.timeIntervalSince(startTime)
    }

    var formattedDuration: String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    var formattedDistance: String {
        if actualDistance < 1000 {
            return String(format: "%.0f米", actualDistance)
        } else {
            return String(format: "%.2f公里", actualDistance / 1000)
        }
    }

    var formattedSpeed: String {
        return String(format: "%.1f m/s", averageSpeed)
    }

    var formattedCompletionRate: String {
        return String(format: "%.1f%%", completionRate * 100)
    }
}

// MARK: - App Configuration

struct AppConfiguration: Codable {
    var defaultAccuracy: Double
    var updateInterval: TimeInterval
    var maxHistoryCount: Int
    var isAutoStartEnabled: Bool
    var mapType: Int // 0: standard, 1: satellite, 2: hybrid
    var showsUserLocation: Bool
    var enableHapticFeedback: Bool
    
    static let `default` = AppConfiguration(
        defaultAccuracy: 5.0,
        updateInterval: 1.0,
        maxHistoryCount: 50,
        isAutoStartEnabled: false,
        mapType: 0,
        showsUserLocation: true,
        enableHapticFeedback: true
    )
}

// MARK: - Predefined Locations

extension DataService {
    
    /// 获取预定义的常用位置
    func getPredefinedLocations() -> [VirtualLocationModel] {
        return [
            VirtualLocationModel(
                name: "北京天安门",
                coordinate: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
                address: "北京市东城区天安门广场"
            ),
            VirtualLocationModel(
                name: "上海外滩",
                coordinate: CLLocationCoordinate2D(latitude: 31.2304, longitude: 121.4737),
                address: "上海市黄浦区外滩"
            ),
            VirtualLocationModel(
                name: "深圳腾讯大厦",
                coordinate: CLLocationCoordinate2D(latitude: 22.5431, longitude: 114.0579),
                address: "深圳市南山区科技园"
            ),
            VirtualLocationModel(
                name: "杭州西湖",
                coordinate: CLLocationCoordinate2D(latitude: 30.2741, longitude: 120.1551),
                address: "浙江省杭州市西湖区"
            ),
            VirtualLocationModel(
                name: "广州塔",
                coordinate: CLLocationCoordinate2D(latitude: 23.1088, longitude: 113.3240),
                address: "广东省广州市海珠区"
            )
        ]
    }
}
