//
//  IntegrationTests.swift
//  VirtualLocationAppTests
//
//  Created by <PERSON><PERSON><PERSON> on 2025-06-20.
//  Copyright © 2025 YourCompany. All rights reserved.
//

import XCTest
import CoreLocation
@testable import VirtualLocationApp

class IntegrationTests: XCTestCase {
    
    // MARK: - Test Properties
    
    var locationService: LocationService!
    var dataService: DataService!
    var pathSimulationService: PathSimulationService!
    var errorHandlingService: ErrorHandlingService!
    
    // MARK: - Setup and Teardown
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        
        locationService = LocationService.shared
        dataService = DataService.shared
        pathSimulationService = PathSimulationService()
        errorHandlingService = ErrorHandlingService.shared
        
        // 清理测试环境
        dataService.clearAllData()
        errorHandlingService.clearErrorLog()
    }
    
    override func tearDownWithError() throws {
        // 停止所有活动的服务
        locationService.stopVirtualLocation()
        pathSimulationService.stopPathSimulation()
        
        // 清理测试数据
        dataService.clearAllData()
        errorHandlingService.clearErrorLog()
        
        try super.tearDownWithError()
    }
    
    // MARK: - Virtual Location Integration Tests
    
    func testVirtualLocationWorkflow() {
        // 测试完整的虚拟定位工作流程
        let expectation = XCTestExpectation(description: "虚拟定位工作流程")
        
        class TestLocationDelegate: LocationServiceDelegate {
            let expectation: XCTestExpectation
            var locationUpdateCount = 0
            
            init(expectation: XCTestExpectation) {
                self.expectation = expectation
            }
            
            func locationService(_ service: LocationService, didUpdateLocation location: CLLocation) {
                locationUpdateCount += 1
                
                // 验证位置数据
                XCTAssertTrue(CLLocationCoordinate2DIsValid(location.coordinate), "位置坐标应该有效")
                XCTAssertGreaterThan(location.horizontalAccuracy, 0, "水平精度应该大于0")
                
                if locationUpdateCount >= 3 {
                    expectation.fulfill()
                }
            }
            
            func locationService(_ service: LocationService, didFailWithError error: Error) {
                XCTFail("不应该发生错误: \(error)")
            }
            
            func locationService(_ service: LocationService, didChangeAuthorizationStatus status: CLAuthorizationStatus) {
                // 授权状态变化处理
            }
        }
        
        let delegate = TestLocationDelegate(expectation: expectation)
        locationService.delegate = delegate
        
        // 开始虚拟定位
        let testCoordinate = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
        locationService.startVirtualLocation(at: testCoordinate, accuracy: 5.0)
        
        wait(for: [expectation], timeout: 5.0)
        
        // 验证虚拟定位状态
        XCTAssertNotNil(locationService.currentLocation, "当前位置不应为空")
        
        // 停止虚拟定位
        locationService.stopVirtualLocation()
    }
    
    func testLocationDataPersistence() {
        // 测试位置数据持久化集成
        let testCoordinate = CLLocationCoordinate2D(latitude: 31.2304, longitude: 121.4737)
        let testLocation = VirtualLocationModel(
            name: "集成测试位置",
            coordinate: testCoordinate,
            accuracy: 10.0,
            address: "上海市黄浦区"
        )
        
        // 保存位置到历史记录
        dataService.saveLocationToHistory(testLocation)
        
        // 验证保存成功
        let history = dataService.getLocationHistory()
        XCTAssertEqual(history.count, 1, "应该有一条历史记录")
        XCTAssertEqual(history.first?.name, "集成测试位置")
        
        // 保存为收藏位置
        dataService.saveFavoriteLocation(testLocation)
        
        // 验证收藏成功
        XCTAssertTrue(dataService.isLocationFavorited(withId: testLocation.id), "位置应该被收藏")
        
        // 保存为最后使用位置
        dataService.saveLastUsedLocation(testLocation)
        
        // 验证最后使用位置
        let lastUsed = dataService.getLastUsedLocation()
        XCTAssertNotNil(lastUsed, "最后使用位置不应为空")
        XCTAssertEqual(lastUsed?.id, testLocation.id, "最后使用位置ID应该匹配")
    }
    
    // MARK: - Path Simulation Integration Tests
    
    func testPathSimulationWorkflow() {
        // 测试路径模拟完整工作流程
        let expectation = XCTestExpectation(description: "路径模拟工作流程")
        
        class TestPathDelegate: PathSimulationDelegate {
            let expectation: XCTestExpectation
            var updateCount = 0
            var lastLocation: CLLocation?
            
            init(expectation: XCTestExpectation) {
                self.expectation = expectation
            }
            
            func pathSimulation(_ service: PathSimulationService, didUpdateLocation location: CLLocation) {
                updateCount += 1
                lastLocation = location
                
                // 验证位置更新
                XCTAssertTrue(CLLocationCoordinate2DIsValid(location.coordinate), "路径位置应该有效")
                XCTAssertGreaterThanOrEqual(location.speed, 0, "速度应该大于等于0")
                
                if updateCount >= 5 {
                    expectation.fulfill()
                }
            }
            
            func pathSimulation(_ service: PathSimulationService, didCompleteWithTotalDistance distance: CLLocationDistance) {
                XCTAssertGreaterThan(distance, 0, "总距离应该大于0")
                expectation.fulfill()
            }
            
            func pathSimulation(_ service: PathSimulationService, didFailWithError error: Error) {
                XCTFail("路径模拟不应该失败: \(error)")
            }
        }
        
        let delegate = TestPathDelegate(expectation: expectation)
        pathSimulationService.delegate = delegate
        
        // 创建测试路径
        let pathPoints = [
            CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
            CLLocationCoordinate2D(latitude: 39.9052, longitude: 116.4084),
            CLLocationCoordinate2D(latitude: 39.9062, longitude: 116.4094),
            CLLocationCoordinate2D(latitude: 39.9072, longitude: 116.4104)
        ]
        
        // 开始路径模拟
        pathSimulationService.startPathSimulation(path: pathPoints, speed: 10.0)
        
        wait(for: [expectation], timeout: 10.0)
        
        // 验证路径模拟状态
        XCTAssertTrue(pathSimulationService.isActive, "路径模拟应该处于活跃状态")
        
        // 停止路径模拟
        pathSimulationService.stopPathSimulation()
        XCTAssertFalse(pathSimulationService.isActive, "路径模拟应该已停止")
    }
    
    func testPathDataManagement() {
        // 测试路径数据管理集成
        let pathPoints = [
            CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
            CLLocationCoordinate2D(latitude: 39.9052, longitude: 116.4084),
            CLLocationCoordinate2D(latitude: 39.9062, longitude: 116.4094)
        ]
        
        let testPath = PathModel(
            name: "集成测试路径",
            points: pathPoints,
            description: "用于集成测试的路径"
        )
        
        // 保存路径
        dataService.savePath(testPath)
        
        // 验证路径保存
        let savedPaths = dataService.getSavedPaths()
        XCTAssertEqual(savedPaths.count, 1, "应该有一条保存的路径")
        XCTAssertEqual(savedPaths.first?.name, "集成测试路径")
        
        // 创建路径历史记录
        let pathHistory = PathHistoryModel(
            pathId: testPath.id,
            pathName: testPath.name,
            startTime: Date().addingTimeInterval(-300), // 5分钟前
            endTime: Date(),
            actualDistance: testPath.totalDistance,
            averageSpeed: 5.0,
            completionRate: 1.0
        )
        
        // 保存路径历史
        dataService.savePathToHistory(pathHistory)
        
        // 验证路径历史
        let pathHistoryList = dataService.getPathHistory()
        XCTAssertEqual(pathHistoryList.count, 1, "应该有一条路径历史记录")
        XCTAssertEqual(pathHistoryList.first?.pathName, "集成测试路径")
    }
    
    // MARK: - Error Handling Integration Tests
    
    func testErrorHandlingIntegration() {
        // 测试错误处理集成
        let expectation = XCTestExpectation(description: "错误处理集成")
        
        class TestLocationDelegate: LocationServiceDelegate {
            let expectation: XCTestExpectation
            
            init(expectation: XCTestExpectation) {
                self.expectation = expectation
            }
            
            func locationService(_ service: LocationService, didUpdateLocation location: CLLocation) {
                // 正常位置更新
            }
            
            func locationService(_ service: LocationService, didFailWithError error: Error) {
                // 验证错误处理
                XCTAssertTrue(error is LocationError, "应该是LocationError类型")
                expectation.fulfill()
            }
            
            func locationService(_ service: LocationService, didChangeAuthorizationStatus status: CLAuthorizationStatus) {
                // 授权状态变化
            }
        }
        
        let delegate = TestLocationDelegate(expectation: expectation)
        locationService.delegate = delegate
        
        // 触发错误（使用无效坐标）
        let invalidCoordinate = CLLocationCoordinate2D(latitude: 200, longitude: 200)
        locationService.startVirtualLocation(at: invalidCoordinate)
        
        wait(for: [expectation], timeout: 2.0)
        
        // 验证错误日志
        let errorLog = errorHandlingService.getErrorLog()
        XCTAssertGreaterThan(errorLog.count, 0, "应该有错误日志记录")
    }
    
    // MARK: - Service Integration Tests
    
    func testLocationServiceWithPathSimulation() {
        // 测试LocationService与PathSimulation的集成
        let expectation = XCTestExpectation(description: "LocationService路径模拟集成")
        
        class TestLocationDelegate: LocationServiceDelegate {
            let expectation: XCTestExpectation
            var updateCount = 0
            
            init(expectation: XCTestExpectation) {
                self.expectation = expectation
            }
            
            func locationService(_ service: LocationService, didUpdateLocation location: CLLocation) {
                updateCount += 1
                
                // 验证路径模拟位置更新
                XCTAssertTrue(CLLocationCoordinate2DIsValid(location.coordinate), "位置应该有效")
                
                if updateCount >= 3 {
                    expectation.fulfill()
                }
            }
            
            func locationService(_ service: LocationService, didFailWithError error: Error) {
                XCTFail("不应该发生错误: \(error)")
            }
            
            func locationService(_ service: LocationService, didChangeAuthorizationStatus status: CLAuthorizationStatus) {}
        }
        
        let delegate = TestLocationDelegate(expectation: expectation)
        locationService.delegate = delegate
        
        // 创建测试路径
        let pathPoints = [
            CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
            CLLocationCoordinate2D(latitude: 39.9052, longitude: 116.4084),
            CLLocationCoordinate2D(latitude: 39.9062, longitude: 116.4094)
        ]
        
        // 通过LocationService启动路径模拟
        locationService.startPathSimulation(path: pathPoints, speed: 5.0)
        
        wait(for: [expectation], timeout: 5.0)
        
        // 验证路径模拟状态
        XCTAssertTrue(locationService.isPathSimulating, "应该正在进行路径模拟")
        
        // 停止路径模拟
        locationService.stopPathSimulation()
        XCTAssertFalse(locationService.isPathSimulating, "路径模拟应该已停止")
    }
    
    func testDataServiceWithErrorHandling() {
        // 测试DataService与ErrorHandling的集成
        
        // 尝试保存无效数据
        let invalidLocation = VirtualLocationModel(
            name: "",
            coordinate: CLLocationCoordinate2D(latitude: 200, longitude: 200)
        )
        
        // 保存前的错误日志数量
        let initialErrorCount = errorHandlingService.getErrorLog().count
        
        // 保存可能触发错误的数据
        dataService.saveLocationToHistory(invalidLocation)
        
        // 验证数据仍然被保存（DataService应该容错）
        let history = dataService.getLocationHistory()
        XCTAssertEqual(history.count, 1, "即使数据无效，也应该被保存")
        
        // 清理测试数据
        dataService.clearLocationHistory()
    }
    
    // MARK: - Configuration Integration Tests
    
    func testConfigurationIntegration() {
        // 测试配置集成
        var config = dataService.getAppConfiguration()
        
        // 修改配置
        config.defaultAccuracy = 15.0
        config.updateInterval = 2.5
        config.maxHistoryCount = 30
        
        // 保存配置
        dataService.saveAppConfiguration(config)
        
        // 应用配置到LocationService
        locationService.setLocationUpdateInterval(config.updateInterval)
        
        // 验证配置应用
        let savedConfig = dataService.getAppConfiguration()
        XCTAssertEqual(savedConfig.defaultAccuracy, 15.0)
        XCTAssertEqual(savedConfig.updateInterval, 2.5)
        XCTAssertEqual(savedConfig.maxHistoryCount, 30)
    }
    
    // MARK: - Stress Tests
    
    func testHighFrequencyLocationUpdates() {
        // 测试高频率位置更新
        let expectation = XCTestExpectation(description: "高频率位置更新")
        
        class TestLocationDelegate: LocationServiceDelegate {
            let expectation: XCTestExpectation
            var updateCount = 0
            let targetCount = 20
            
            init(expectation: XCTestExpectation) {
                self.expectation = expectation
            }
            
            func locationService(_ service: LocationService, didUpdateLocation location: CLLocation) {
                updateCount += 1
                if updateCount >= targetCount {
                    expectation.fulfill()
                }
            }
            
            func locationService(_ service: LocationService, didFailWithError error: Error) {}
            func locationService(_ service: LocationService, didChangeAuthorizationStatus status: CLAuthorizationStatus) {}
        }
        
        let delegate = TestLocationDelegate(expectation: expectation)
        locationService.delegate = delegate
        
        // 设置高频率更新
        locationService.setLocationUpdateInterval(0.1) // 100ms
        
        let testCoordinate = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
        locationService.startVirtualLocation(at: testCoordinate)
        
        wait(for: [expectation], timeout: 5.0)
        
        locationService.stopVirtualLocation()
    }
    
    func testMultiplePathSimulations() {
        // 测试多个路径模拟的处理
        let pathPoints1 = [
            CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
            CLLocationCoordinate2D(latitude: 39.9052, longitude: 116.4084)
        ]
        
        let pathPoints2 = [
            CLLocationCoordinate2D(latitude: 31.2304, longitude: 121.4737),
            CLLocationCoordinate2D(latitude: 31.2314, longitude: 121.4747)
        ]
        
        // 创建多个路径
        let path1 = PathModel(name: "路径1", points: pathPoints1)
        let path2 = PathModel(name: "路径2", points: pathPoints2)
        
        // 保存路径
        dataService.savePath(path1)
        dataService.savePath(path2)
        
        // 验证多个路径保存
        let savedPaths = dataService.getSavedPaths()
        XCTAssertEqual(savedPaths.count, 2, "应该有两条保存的路径")
        
        // 验证路径可以被正确检索
        let retrievedPath1 = savedPaths.first { $0.name == "路径1" }
        let retrievedPath2 = savedPaths.first { $0.name == "路径2" }
        
        XCTAssertNotNil(retrievedPath1, "路径1应该被找到")
        XCTAssertNotNil(retrievedPath2, "路径2应该被找到")
    }
}
