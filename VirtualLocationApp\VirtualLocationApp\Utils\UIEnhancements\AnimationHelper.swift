//
//  AnimationHelper.swift
//  VirtualLocationApp
//
//  Created by <PERSON><PERSON><PERSON> on 2025-06-20.
//  Copyright © 2025 YourCompany. All rights reserved.
//

import UIKit

// MARK: - Animation Helper

class AnimationHelper {
    
    // MARK: - Button Animations
    
    /// 按钮点击动画
    static func animateButtonTap(_ button: UIButton, completion: (() -> Void)? = nil) {
        UIView.animate(withDuration: 0.1, animations: {
            button.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1, animations: {
                button.transform = .identity
            }) { _ in
                completion?()
            }
        }
    }
    
    /// 按钮成功动画
    static func animateButtonSuccess(_ button: UIButton) {
        let originalColor = button.backgroundColor
        
        UIView.animate(withDuration: 0.2, animations: {
            button.backgroundColor = UIColor.systemGreen
            button.transform = CGAffineTransform(scaleX: 1.05, y: 1.05)
        }) { _ in
            UIView.animate(withDuration: 0.3, animations: {
                button.backgroundColor = originalColor
                button.transform = .identity
            })
        }
    }
    
    /// 按钮错误动画
    static func animateButtonError(_ button: UIButton) {
        let originalColor = button.backgroundColor
        
        UIView.animate(withDuration: 0.1, animations: {
            button.backgroundColor = UIColor.systemRed
            button.transform = CGAffineTransform(translationX: -5, y: 0)
        }) { _ in
            UIView.animate(withDuration: 0.1, animations: {
                button.transform = CGAffineTransform(translationX: 5, y: 0)
            }) { _ in
                UIView.animate(withDuration: 0.1, animations: {
                    button.transform = .identity
                    button.backgroundColor = originalColor
                })
            }
        }
    }
    
    // MARK: - View Animations
    
    /// 淡入动画
    static func fadeIn(_ view: UIView, duration: TimeInterval = 0.3, completion: (() -> Void)? = nil) {
        view.alpha = 0
        view.isHidden = false
        
        UIView.animate(withDuration: duration, animations: {
            view.alpha = 1
        }) { _ in
            completion?()
        }
    }
    
    /// 淡出动画
    static func fadeOut(_ view: UIView, duration: TimeInterval = 0.3, completion: (() -> Void)? = nil) {
        UIView.animate(withDuration: duration, animations: {
            view.alpha = 0
        }) { _ in
            view.isHidden = true
            completion?()
        }
    }
    
    /// 滑入动画
    static func slideIn(_ view: UIView, from direction: SlideDirection, duration: TimeInterval = 0.3, completion: (() -> Void)? = nil) {
        let originalTransform = view.transform
        
        switch direction {
        case .top:
            view.transform = CGAffineTransform(translationX: 0, y: -view.bounds.height)
        case .bottom:
            view.transform = CGAffineTransform(translationX: 0, y: view.bounds.height)
        case .left:
            view.transform = CGAffineTransform(translationX: -view.bounds.width, y: 0)
        case .right:
            view.transform = CGAffineTransform(translationX: view.bounds.width, y: 0)
        }
        
        view.isHidden = false
        
        UIView.animate(withDuration: duration, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: .curveEaseOut, animations: {
            view.transform = originalTransform
        }) { _ in
            completion?()
        }
    }
    
    /// 滑出动画
    static func slideOut(_ view: UIView, to direction: SlideDirection, duration: TimeInterval = 0.3, completion: (() -> Void)? = nil) {
        UIView.animate(withDuration: duration, animations: {
            switch direction {
            case .top:
                view.transform = CGAffineTransform(translationX: 0, y: -view.bounds.height)
            case .bottom:
                view.transform = CGAffineTransform(translationX: 0, y: view.bounds.height)
            case .left:
                view.transform = CGAffineTransform(translationX: -view.bounds.width, y: 0)
            case .right:
                view.transform = CGAffineTransform(translationX: view.bounds.width, y: 0)
            }
        }) { _ in
            view.isHidden = true
            view.transform = .identity
            completion?()
        }
    }
    
    /// 弹跳动画
    static func bounce(_ view: UIView, completion: (() -> Void)? = nil) {
        UIView.animate(withDuration: 0.2, animations: {
            view.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        }) { _ in
            UIView.animate(withDuration: 0.2, animations: {
                view.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
            }) { _ in
                UIView.animate(withDuration: 0.1, animations: {
                    view.transform = .identity
                }) { _ in
                    completion?()
                }
            }
        }
    }
    
    /// 摇摆动画
    static func shake(_ view: UIView, completion: (() -> Void)? = nil) {
        let animation = CAKeyframeAnimation(keyPath: "transform.translation.x")
        animation.timingFunction = CAMediaTimingFunction(name: .linear)
        animation.duration = 0.6
        animation.values = [-20.0, 20.0, -20.0, 20.0, -10.0, 10.0, -5.0, 5.0, 0.0]
        
        CATransaction.begin()
        CATransaction.setCompletionBlock {
            completion?()
        }
        view.layer.add(animation, forKey: "shake")
        CATransaction.commit()
    }
    
    // MARK: - Loading Animations
    
    /// 旋转加载动画
    static func startRotationAnimation(_ view: UIView) {
        let rotation = CABasicAnimation(keyPath: "transform.rotation")
        rotation.fromValue = 0
        rotation.toValue = Double.pi * 2
        rotation.duration = 1.0
        rotation.repeatCount = .infinity
        view.layer.add(rotation, forKey: "rotation")
    }
    
    /// 停止旋转动画
    static func stopRotationAnimation(_ view: UIView) {
        view.layer.removeAnimation(forKey: "rotation")
    }
    
    /// 脉冲动画
    static func startPulseAnimation(_ view: UIView) {
        let pulse = CABasicAnimation(keyPath: "transform.scale")
        pulse.fromValue = 1.0
        pulse.toValue = 1.1
        pulse.duration = 1.0
        pulse.autoreverses = true
        pulse.repeatCount = .infinity
        view.layer.add(pulse, forKey: "pulse")
    }
    
    /// 停止脉冲动画
    static func stopPulseAnimation(_ view: UIView) {
        view.layer.removeAnimation(forKey: "pulse")
    }
}

// MARK: - Slide Direction

enum SlideDirection {
    case top
    case bottom
    case left
    case right
}

// MARK: - Haptic Feedback Helper

class HapticFeedbackHelper {
    
    /// 轻微触觉反馈
    static func lightImpact() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    /// 中等触觉反馈
    static func mediumImpact() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    /// 重度触觉反馈
    static func heavyImpact() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()
    }
    
    /// 成功反馈
    static func success() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)
    }
    
    /// 警告反馈
    static func warning() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.warning)
    }
    
    /// 错误反馈
    static func error() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.error)
    }
    
    /// 选择反馈
    static func selection() {
        let selectionFeedback = UISelectionFeedbackGenerator()
        selectionFeedback.selectionChanged()
    }
}

// MARK: - Toast Helper

class ToastHelper {
    
    static func showToast(message: String, in view: UIView, duration: TimeInterval = 2.0) {
        // 移除之前的toast
        view.subviews.filter { $0.tag == 999 }.forEach { $0.removeFromSuperview() }
        
        let toastView = UIView()
        toastView.tag = 999
        toastView.backgroundColor = UIColor.black.withAlphaComponent(0.8)
        toastView.layer.cornerRadius = 8
        toastView.translatesAutoresizingMaskIntoConstraints = false
        
        let messageLabel = UILabel()
        messageLabel.text = message
        messageLabel.textColor = .white
        messageLabel.font = UIFont.systemFont(ofSize: 14)
        messageLabel.textAlignment = .center
        messageLabel.numberOfLines = 0
        messageLabel.translatesAutoresizingMaskIntoConstraints = false
        
        toastView.addSubview(messageLabel)
        view.addSubview(toastView)
        
        NSLayoutConstraint.activate([
            messageLabel.topAnchor.constraint(equalTo: toastView.topAnchor, constant: 12),
            messageLabel.leadingAnchor.constraint(equalTo: toastView.leadingAnchor, constant: 16),
            messageLabel.trailingAnchor.constraint(equalTo: toastView.trailingAnchor, constant: -16),
            messageLabel.bottomAnchor.constraint(equalTo: toastView.bottomAnchor, constant: -12),
            
            toastView.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            toastView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -100),
            toastView.leadingAnchor.constraint(greaterThanOrEqualTo: view.leadingAnchor, constant: 32),
            toastView.trailingAnchor.constraint(lessThanOrEqualTo: view.trailingAnchor, constant: -32)
        ])
        
        // 动画显示
        toastView.alpha = 0
        toastView.transform = CGAffineTransform(translationX: 0, y: 20)
        
        UIView.animate(withDuration: 0.3, animations: {
            toastView.alpha = 1
            toastView.transform = .identity
        }) { _ in
            // 延迟后隐藏
            DispatchQueue.main.asyncAfter(deadline: .now() + duration) {
                UIView.animate(withDuration: 0.3, animations: {
                    toastView.alpha = 0
                    toastView.transform = CGAffineTransform(translationX: 0, y: 20)
                }) { _ in
                    toastView.removeFromSuperview()
                }
            }
        }
    }
}
