//
//  TestReportGenerator.swift
//  VirtualLocationApp
//
//  Created by <PERSON><PERSON><PERSON> on 2025-06-20.
//  Copyright © 2025 YourCompany. All rights reserved.
//

import Foundation
import UIKit

// MARK: - Test Report Generator

class TestReportGenerator {
    
    // MARK: - Singleton
    
    static let shared = TestReportGenerator()
    
    // MARK: - Properties
    
    private var testResults: [TestResult] = []
    private var performanceMetrics: [PerformanceMetric] = []
    private var compatibilityResults: [CompatibilityResult] = []
    private var userExperienceResults: [UserExperienceResult] = []
    
    // MARK: - Initialization
    
    private init() {}
    
    // MARK: - Public Methods
    
    /// 添加测试结果
    func addTestResult(_ result: TestResult) {
        testResults.append(result)
    }
    
    /// 添加性能指标
    func addPerformanceMetric(_ metric: PerformanceMetric) {
        performanceMetrics.append(metric)
    }
    
    /// 添加兼容性结果
    func addCompatibilityResult(_ result: CompatibilityResult) {
        compatibilityResults.append(result)
    }
    
    /// 添加用户体验结果
    func addUserExperienceResult(_ result: UserExperienceResult) {
        userExperienceResults.append(result)
    }
    
    /// 生成完整测试报告
    func generateCompleteReport() -> TestReport {
        let summary = generateSummary()
        let functionalTestReport = generateFunctionalTestReport()
        let performanceReport = generatePerformanceReport()
        let compatibilityReport = generateCompatibilityReport()
        let userExperienceReport = generateUserExperienceReport()
        
        return TestReport(
            summary: summary,
            functionalTests: functionalTestReport,
            performanceTests: performanceReport,
            compatibilityTests: compatibilityReport,
            userExperienceTests: userExperienceReport,
            generatedAt: Date()
        )
    }
    
    /// 导出测试报告为文本
    func exportReportAsText() -> String {
        let report = generateCompleteReport()
        
        var reportText = """
        =====================================
        虚拟定位应用测试报告
        =====================================
        
        生成时间: \(formatDate(report.generatedAt))
        
        """
        
        // 添加摘要
        reportText += generateSummaryText(report.summary)
        
        // 添加功能测试报告
        reportText += generateFunctionalTestText(report.functionalTests)
        
        // 添加性能测试报告
        reportText += generatePerformanceTestText(report.performanceTests)
        
        // 添加兼容性测试报告
        reportText += generateCompatibilityTestText(report.compatibilityTests)
        
        // 添加用户体验测试报告
        reportText += generateUserExperienceTestText(report.userExperienceTests)
        
        return reportText
    }
    
    /// 保存测试报告到文件
    func saveReportToFile() -> URL? {
        let reportText = exportReportAsText()
        
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let fileName = "VirtualLocationApp_TestReport_\(formatDateForFilename(Date())).txt"
        let fileURL = documentsPath.appendingPathComponent(fileName)
        
        do {
            try reportText.write(to: fileURL, atomically: true, encoding: .utf8)
            return fileURL
        } catch {
            print("❌ 保存测试报告失败: \(error)")
            return nil
        }
    }
    
    /// 清空所有测试数据
    func clearAllData() {
        testResults.removeAll()
        performanceMetrics.removeAll()
        compatibilityResults.removeAll()
        userExperienceResults.removeAll()
    }
    
    // MARK: - Private Methods
    
    private func generateSummary() -> TestSummary {
        let totalTests = testResults.count
        let passedTests = testResults.filter { $0.status == .passed }.count
        let failedTests = testResults.filter { $0.status == .failed }.count
        let skippedTests = testResults.filter { $0.status == .skipped }.count
        
        let successRate = totalTests > 0 ? Double(passedTests) / Double(totalTests) : 0.0
        
        return TestSummary(
            totalTests: totalTests,
            passedTests: passedTests,
            failedTests: failedTests,
            skippedTests: skippedTests,
            successRate: successRate,
            executionTime: calculateTotalExecutionTime()
        )
    }
    
    private func generateFunctionalTestReport() -> FunctionalTestReport {
        let functionalTests = testResults.filter { $0.category == .functional }
        
        return FunctionalTestReport(
            totalTests: functionalTests.count,
            passedTests: functionalTests.filter { $0.status == .passed }.count,
            failedTests: functionalTests.filter { $0.status == .failed }.count,
            testDetails: functionalTests
        )
    }
    
    private func generatePerformanceReport() -> PerformanceTestReport {
        let performanceTests = testResults.filter { $0.category == .performance }
        
        let averageMemoryUsage = performanceMetrics.isEmpty ? 0 : performanceMetrics.reduce(0) { $0 + $1.memoryUsage } / UInt64(performanceMetrics.count)
        let averageCPUUsage = performanceMetrics.isEmpty ? 0 : performanceMetrics.reduce(0) { $0 + $1.cpuUsage } / Double(performanceMetrics.count)
        
        return PerformanceTestReport(
            totalTests: performanceTests.count,
            passedTests: performanceTests.filter { $0.status == .passed }.count,
            failedTests: performanceTests.filter { $0.status == .failed }.count,
            averageMemoryUsage: averageMemoryUsage,
            averageCPUUsage: averageCPUUsage,
            testDetails: performanceTests
        )
    }
    
    private func generateCompatibilityReport() -> CompatibilityTestReport {
        return CompatibilityTestReport(
            iosVersions: compatibilityResults.filter { $0.type == .iosVersion },
            deviceTypes: compatibilityResults.filter { $0.type == .deviceType },
            networkConditions: compatibilityResults.filter { $0.type == .networkCondition },
            overallCompatibility: calculateOverallCompatibility()
        )
    }
    
    private func generateUserExperienceReport() -> UserExperienceTestReport {
        let uxTests = testResults.filter { $0.category == .userExperience }
        
        return UserExperienceTestReport(
            totalTests: uxTests.count,
            passedTests: uxTests.filter { $0.status == .passed }.count,
            failedTests: uxTests.filter { $0.status == .failed }.count,
            averageResponseTime: calculateAverageResponseTime(),
            accessibilityScore: calculateAccessibilityScore(),
            testDetails: uxTests
        )
    }
    
    private func calculateTotalExecutionTime() -> TimeInterval {
        return testResults.reduce(0) { $0 + $1.executionTime }
    }
    
    private func calculateOverallCompatibility() -> Double {
        guard !compatibilityResults.isEmpty else { return 0.0 }
        
        let passedResults = compatibilityResults.filter { $0.status == .passed }.count
        return Double(passedResults) / Double(compatibilityResults.count)
    }
    
    private func calculateAverageResponseTime() -> TimeInterval {
        let responseTimeResults = userExperienceResults.filter { $0.type == .responseTime }
        guard !responseTimeResults.isEmpty else { return 0.0 }
        
        let totalTime = responseTimeResults.reduce(0) { $0 + $1.value }
        return totalTime / Double(responseTimeResults.count)
    }
    
    private func calculateAccessibilityScore() -> Double {
        let accessibilityResults = userExperienceResults.filter { $0.type == .accessibility }
        guard !accessibilityResults.isEmpty else { return 0.0 }
        
        let totalScore = accessibilityResults.reduce(0) { $0 + $1.value }
        return totalScore / Double(accessibilityResults.count)
    }
    
    // MARK: - Text Generation Methods
    
    private func generateSummaryText(_ summary: TestSummary) -> String {
        return """
        
        =====================================
        测试摘要
        =====================================
        
        总测试数: \(summary.totalTests)
        通过测试: \(summary.passedTests)
        失败测试: \(summary.failedTests)
        跳过测试: \(summary.skippedTests)
        成功率: \(String(format: "%.2f%%", summary.successRate * 100))
        总执行时间: \(String(format: "%.2f秒", summary.executionTime))
        
        """
    }
    
    private func generateFunctionalTestText(_ report: FunctionalTestReport) -> String {
        var text = """
        
        =====================================
        功能测试报告
        =====================================
        
        总测试数: \(report.totalTests)
        通过测试: \(report.passedTests)
        失败测试: \(report.failedTests)
        
        测试详情:
        
        """
        
        for test in report.testDetails {
            text += "- \(test.name): \(test.status.rawValue) (\(String(format: "%.3f秒", test.executionTime)))\n"
            if let errorMessage = test.errorMessage {
                text += "  错误: \(errorMessage)\n"
            }
        }
        
        return text + "\n"
    }
    
    private func generatePerformanceTestText(_ report: PerformanceTestReport) -> String {
        return """
        
        =====================================
        性能测试报告
        =====================================
        
        总测试数: \(report.totalTests)
        通过测试: \(report.passedTests)
        失败测试: \(report.failedTests)
        平均内存使用: \(formatBytes(report.averageMemoryUsage))
        平均CPU使用: \(String(format: "%.2f%%", report.averageCPUUsage))
        
        """
    }
    
    private func generateCompatibilityTestText(_ report: CompatibilityTestReport) -> String {
        return """
        
        =====================================
        兼容性测试报告
        =====================================
        
        iOS版本兼容性: \(report.iosVersions.count)项测试
        设备类型兼容性: \(report.deviceTypes.count)项测试
        网络环境兼容性: \(report.networkConditions.count)项测试
        总体兼容性: \(String(format: "%.2f%%", report.overallCompatibility * 100))
        
        """
    }
    
    private func generateUserExperienceTestText(_ report: UserExperienceTestReport) -> String {
        return """
        
        =====================================
        用户体验测试报告
        =====================================
        
        总测试数: \(report.totalTests)
        通过测试: \(report.passedTests)
        失败测试: \(report.failedTests)
        平均响应时间: \(String(format: "%.3f秒", report.averageResponseTime))
        无障碍评分: \(String(format: "%.2f", report.accessibilityScore))
        
        """
    }
    
    // MARK: - Utility Methods
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .medium
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
    
    private func formatDateForFilename(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd_HHmmss"
        return formatter.string(from: date)
    }
    
    private func formatBytes(_ bytes: UInt64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB, .useGB]
        formatter.countStyle = .memory
        return formatter.string(fromByteCount: Int64(bytes))
    }
}

// MARK: - Test Models

struct TestResult {
    let name: String
    let category: TestCategory
    let status: TestStatus
    let executionTime: TimeInterval
    let errorMessage: String?
    let timestamp: Date
    
    init(name: String, category: TestCategory, status: TestStatus, executionTime: TimeInterval, errorMessage: String? = nil) {
        self.name = name
        self.category = category
        self.status = status
        self.executionTime = executionTime
        self.errorMessage = errorMessage
        self.timestamp = Date()
    }
}

struct CompatibilityResult {
    let type: CompatibilityType
    let name: String
    let status: TestStatus
    let details: String?
    
    enum CompatibilityType {
        case iosVersion
        case deviceType
        case networkCondition
    }
}

struct UserExperienceResult {
    let type: UserExperienceType
    let name: String
    let value: Double
    let status: TestStatus
    
    enum UserExperienceType {
        case responseTime
        case accessibility
        case animation
        case hapticFeedback
    }
}

enum TestCategory {
    case functional
    case performance
    case compatibility
    case userExperience
}

enum TestStatus: String {
    case passed = "通过"
    case failed = "失败"
    case skipped = "跳过"
}

// MARK: - Report Models

struct TestReport {
    let summary: TestSummary
    let functionalTests: FunctionalTestReport
    let performanceTests: PerformanceTestReport
    let compatibilityTests: CompatibilityTestReport
    let userExperienceTests: UserExperienceTestReport
    let generatedAt: Date
}

struct TestSummary {
    let totalTests: Int
    let passedTests: Int
    let failedTests: Int
    let skippedTests: Int
    let successRate: Double
    let executionTime: TimeInterval
}

struct FunctionalTestReport {
    let totalTests: Int
    let passedTests: Int
    let failedTests: Int
    let testDetails: [TestResult]
}

struct PerformanceTestReport {
    let totalTests: Int
    let passedTests: Int
    let failedTests: Int
    let averageMemoryUsage: UInt64
    let averageCPUUsage: Double
    let testDetails: [TestResult]
}

struct CompatibilityTestReport {
    let iosVersions: [CompatibilityResult]
    let deviceTypes: [CompatibilityResult]
    let networkConditions: [CompatibilityResult]
    let overallCompatibility: Double
}

struct UserExperienceTestReport {
    let totalTests: Int
    let passedTests: Int
    let failedTests: Int
    let averageResponseTime: TimeInterval
    let accessibilityScore: Double
    let testDetails: [TestResult]
}
