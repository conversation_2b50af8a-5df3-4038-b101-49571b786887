//
//  VirtualLocationAppUITests.swift
//  VirtualLocationAppUITests
//
//  Created by Developer on 2025-06-20.
//  Copyright © 2025 YourCompany. All rights reserved.
//

import XCTest

final class VirtualLocationAppUITests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        
        continueAfterFailure = false
        
        app = XCUIApplication()
        app.launch()
    }
    
    override func tearDownWithError() throws {
        app = nil
        try super.tearDownWithError()
    }
    
    // MARK: - App Launch Tests
    
    func testAppLaunch() {
        // 测试应用启动
        XCTAssertTrue(app.maps.firstMatch.exists, "地图视图应该存在")
        XCTAssertTrue(app.buttons["开始虚拟定位"].exists, "开始按钮应该存在")
        XCTAssertTrue(app.buttons["停止虚拟定位"].exists, "停止按钮应该存在")
    }
    
    func testNavigationBarElements() {
        // 测试导航栏元素
        XCTAssertTrue(app.navigationBars.firstMatch.exists, "导航栏应该存在")
        XCTAssertTrue(app.buttons["设置"].exists || app.images["gearshape"].exists, "设置按钮应该存在")
        XCTAssertTrue(app.buttons["历史记录"].exists || app.images["clock"].exists, "历史记录按钮应该存在")
    }
    
    // MARK: - Map Interaction Tests
    
    func testMapInteraction() {
        // 测试地图交互
        let map = app.maps.firstMatch
        XCTAssertTrue(map.exists, "地图应该存在")
        
        // 点击地图
        map.tap()
        
        // 验证坐标标签更新
        let coordinateLabel = app.staticTexts.containing(NSPredicate(format: "label CONTAINS '纬度'")).firstMatch
        
        // 等待坐标更新
        let expectation = XCTNSPredicateExpectation(
            predicate: NSPredicate(format: "exists == true"),
            object: coordinateLabel
        )
        
        wait(for: [expectation], timeout: 3.0)
    }
    
    func testMapTypeControl() {
        // 测试地图类型控制
        let segmentedControl = app.segmentedControls.firstMatch
        
        if segmentedControl.exists {
            // 测试切换到卫星视图
            segmentedControl.buttons["卫星"].tap()
            
            // 测试切换到混合视图
            segmentedControl.buttons["混合"].tap()
            
            // 切换回标准视图
            segmentedControl.buttons["标准"].tap()
        }
    }
    
    // MARK: - Virtual Location Tests
    
    func testVirtualLocationWorkflow() {
        // 测试虚拟定位工作流程
        let map = app.maps.firstMatch
        let startButton = app.buttons["开始虚拟定位"]
        let stopButton = app.buttons["停止虚拟定位"]
        
        // 1. 点击地图选择位置
        map.tap()
        
        // 2. 等待开始按钮可用
        let startButtonEnabled = XCTNSPredicateExpectation(
            predicate: NSPredicate(format: "isEnabled == true"),
            object: startButton
        )
        wait(for: [startButtonEnabled], timeout: 2.0)
        
        // 3. 点击开始按钮
        startButton.tap()
        
        // 4. 验证停止按钮变为可用
        let stopButtonEnabled = XCTNSPredicateExpectation(
            predicate: NSPredicate(format: "isEnabled == true"),
            object: stopButton
        )
        wait(for: [stopButtonEnabled], timeout: 2.0)
        
        // 5. 点击停止按钮
        stopButton.tap()
        
        // 6. 验证开始按钮重新可用
        let startButtonReEnabled = XCTNSPredicateExpectation(
            predicate: NSPredicate(format: "isEnabled == true"),
            object: startButton
        )
        wait(for: [startButtonReEnabled], timeout: 2.0)
    }
    
    func testAccuracySlider() {
        // 测试精度滑块
        let slider = app.sliders.firstMatch
        
        if slider.exists {
            // 调整滑块值
            slider.adjust(toNormalizedSliderPosition: 0.8)
            
            // 验证精度标签更新
            let accuracyLabel = app.staticTexts.containing(NSPredicate(format: "label CONTAINS '精度'")).firstMatch
            XCTAssertTrue(accuracyLabel.exists, "精度标签应该存在")
        }
    }
    
    // MARK: - Settings Tests
    
    func testSettingsNavigation() {
        // 测试设置页面导航
        let settingsButton = app.buttons.matching(identifier: "设置").firstMatch
        
        if !settingsButton.exists {
            // 尝试通过图标查找
            let gearIcon = app.images["gearshape"].firstMatch
            if gearIcon.exists {
                gearIcon.tap()
            }
        } else {
            settingsButton.tap()
        }
        
        // 验证设置页面出现
        let settingsTitle = app.staticTexts["设置"]
        let settingsTitleExpectation = XCTNSPredicateExpectation(
            predicate: NSPredicate(format: "exists == true"),
            object: settingsTitle
        )
        wait(for: [settingsTitleExpectation], timeout: 3.0)
        
        // 关闭设置页面
        let doneButton = app.buttons["完成"]
        if doneButton.exists {
            doneButton.tap()
        }
    }
    
    func testSettingsInteraction() {
        // 测试设置页面交互
        let settingsButton = app.buttons.matching(identifier: "设置").firstMatch
        
        if settingsButton.exists {
            settingsButton.tap()
            
            // 测试开关控件
            let switches = app.switches
            if switches.count > 0 {
                let firstSwitch = switches.firstMatch
                let initialValue = firstSwitch.value as? String
                
                firstSwitch.tap()
                
                // 验证开关状态改变
                let newValue = firstSwitch.value as? String
                XCTAssertNotEqual(initialValue, newValue, "开关状态应该改变")
            }
            
            // 关闭设置页面
            let doneButton = app.buttons["完成"]
            if doneButton.exists {
                doneButton.tap()
            }
        }
    }
    
    // MARK: - History Tests
    
    func testHistoryNavigation() {
        // 测试历史记录页面导航
        let historyButton = app.buttons.matching(identifier: "历史记录").firstMatch
        
        if !historyButton.exists {
            // 尝试通过图标查找
            let clockIcon = app.images["clock"].firstMatch
            if clockIcon.exists {
                clockIcon.tap()
            }
        } else {
            historyButton.tap()
        }
        
        // 验证历史记录页面出现
        let historyTitle = app.staticTexts["位置管理"]
        let historyTitleExpectation = XCTNSPredicateExpectation(
            predicate: NSPredicate(format: "exists == true"),
            object: historyTitle
        )
        wait(for: [historyTitleExpectation], timeout: 3.0)
        
        // 关闭历史记录页面
        let doneButton = app.buttons["完成"]
        if doneButton.exists {
            doneButton.tap()
        }
    }
    
    func testHistorySegmentedControl() {
        // 测试历史记录分段控制
        let historyButton = app.buttons.matching(identifier: "历史记录").firstMatch
        
        if historyButton.exists {
            historyButton.tap()
            
            // 测试分段控制器
            let segmentedControl = app.segmentedControls.firstMatch
            if segmentedControl.exists {
                // 切换到收藏位置
                if segmentedControl.buttons["收藏位置"].exists {
                    segmentedControl.buttons["收藏位置"].tap()
                }
                
                // 切换到预设位置
                if segmentedControl.buttons["预设位置"].exists {
                    segmentedControl.buttons["预设位置"].tap()
                }
                
                // 切换回历史记录
                if segmentedControl.buttons["历史记录"].exists {
                    segmentedControl.buttons["历史记录"].tap()
                }
            }
            
            // 关闭页面
            let doneButton = app.buttons["完成"]
            if doneButton.exists {
                doneButton.tap()
            }
        }
    }
    
    // MARK: - Search Tests
    
    func testSearchFunctionality() {
        // 测试搜索功能
        let searchButton = app.buttons.matching(identifier: "搜索").firstMatch
        
        if !searchButton.exists {
            // 尝试通过图标查找
            let searchIcon = app.images["magnifyingglass"].firstMatch
            if searchIcon.exists {
                searchIcon.tap()
            }
        } else {
            searchButton.tap()
        }
        
        // 查找搜索栏
        let searchField = app.searchFields.firstMatch
        if searchField.exists {
            searchField.tap()
            searchField.typeText("北京")
            
            // 点击搜索按钮
            app.keyboards.buttons["搜索"].tap()
        }
    }
    
    // MARK: - Performance Tests
    
    func testAppLaunchPerformance() {
        // 测试应用启动性能
        measure(metrics: [XCTApplicationLaunchMetric()]) {
            XCUIApplication().launch()
        }
    }
    
    func testMapRenderingPerformance() {
        // 测试地图渲染性能
        let map = app.maps.firstMatch
        
        measure(metrics: [XCTCPUMetric(), XCTMemoryMetric()]) {
            // 执行地图交互操作
            for _ in 0..<10 {
                map.pinch(withScale: 2.0, velocity: 1.0)
                map.pinch(withScale: 0.5, velocity: 1.0)
            }
        }
    }
    
    // MARK: - Accessibility Tests
    
    func testAccessibility() {
        // 测试无障碍功能
        let map = app.maps.firstMatch
        XCTAssertTrue(map.isAccessibilityElement || map.exists, "地图应该可访问")
        
        let startButton = app.buttons["开始虚拟定位"]
        XCTAssertTrue(startButton.isAccessibilityElement, "开始按钮应该可访问")
        
        let stopButton = app.buttons["停止虚拟定位"]
        XCTAssertTrue(stopButton.isAccessibilityElement, "停止按钮应该可访问")
    }
    
    // MARK: - Error Handling Tests
    
    func testErrorHandling() {
        // 测试错误处理UI
        let startButton = app.buttons["开始虚拟定位"]
        
        // 在没有选择位置的情况下点击开始按钮
        startButton.tap()
        
        // 验证错误提示出现
        let alert = app.alerts.firstMatch
        let alertExpectation = XCTNSPredicateExpectation(
            predicate: NSPredicate(format: "exists == true"),
            object: alert
        )
        wait(for: [alertExpectation], timeout: 3.0)
        
        // 关闭错误提示
        if alert.exists {
            let okButton = alert.buttons["确定"]
            if okButton.exists {
                okButton.tap()
            }
        }
    }
    
    // MARK: - Rotation Tests
    
    func testDeviceRotation() {
        // 测试设备旋转
        let device = XCUIDevice.shared
        
        // 旋转到横屏
        device.orientation = .landscapeLeft
        
        // 验证界面适配
        let map = app.maps.firstMatch
        XCTAssertTrue(map.exists, "地图在横屏模式下应该存在")
        
        // 旋转回竖屏
        device.orientation = .portrait
        
        // 验证界面恢复
        XCTAssertTrue(map.exists, "地图在竖屏模式下应该存在")
    }
}
