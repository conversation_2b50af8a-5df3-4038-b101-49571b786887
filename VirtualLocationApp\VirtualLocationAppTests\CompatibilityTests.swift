//
//  CompatibilityTests.swift
//  VirtualLocationAppTests
//
//  Created by Developer on 2025-06-20.
//  Copyright © 2025 YourCompany. All rights reserved.
//

import XCTest
import CoreLocation
import UIKit
@testable import VirtualLocationApp

class CompatibilityTests: XCTestCase {
    
    // MARK: - Test Properties
    
    var locationService: LocationService!
    var dataService: DataService!
    var errorHandlingService: ErrorHandlingService!
    
    // MARK: - Setup and Teardown
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        
        locationService = LocationService.shared
        dataService = DataService.shared
        errorHandlingService = ErrorHandlingService.shared
        
        // 清理测试环境
        dataService.clearAllData()
        errorHandlingService.clearErrorLog()
    }
    
    override func tearDownWithError() throws {
        // 清理测试数据
        locationService.stopVirtualLocation()
        dataService.clearAllData()
        errorHandlingService.clearErrorLog()
        
        try super.tearDownWithError()
    }
    
    // MARK: - iOS Version Compatibility Tests
    
    func testIOSVersionCompatibility() {
        // 测试iOS版本兼容性
        let systemVersion = UIDevice.current.systemVersion
        let versionComponents = systemVersion.components(separatedBy: ".")
        
        guard let majorVersion = versionComponents.first,
              let majorVersionInt = Int(majorVersion) else {
            XCTFail("无法获取iOS主版本号")
            return
        }
        
        print("📱 当前iOS版本: \(systemVersion)")
        
        // 验证支持的最低版本
        XCTAssertGreaterThanOrEqual(majorVersionInt, 15, "应用要求iOS 15.5+")
        
        // 测试版本特定功能
        if majorVersionInt >= 15 {
            testIOS15Features()
        }
        
        if majorVersionInt >= 16 {
            testIOS16Features()
        }
        
        if majorVersionInt >= 17 {
            testIOS17Features()
        }
    }
    
    private func testIOS15Features() {
        // 测试iOS 15特定功能
        print("✅ 测试iOS 15功能")
        
        // 测试Core Location功能
        XCTAssertTrue(CLLocationManager.locationServicesEnabled() != nil, "Core Location应该可用")
        
        // 测试MapKit功能
        XCTAssertNotNil(NSClassFromString("MKMapView"), "MapKit应该可用")
        
        // 测试UserDefaults功能
        let testKey = "iOS15CompatibilityTest"
        UserDefaults.standard.set("test", forKey: testKey)
        XCTAssertEqual(UserDefaults.standard.string(forKey: testKey), "test", "UserDefaults应该正常工作")
        UserDefaults.standard.removeObject(forKey: testKey)
    }
    
    private func testIOS16Features() {
        // 测试iOS 16特定功能
        print("✅ 测试iOS 16功能")
        
        // 测试新的位置权限API
        if #available(iOS 16.0, *) {
            // iOS 16特定的测试
            XCTAssertTrue(true, "iOS 16功能测试通过")
        }
    }
    
    private func testIOS17Features() {
        // 测试iOS 17特定功能
        print("✅ 测试iOS 17功能")
        
        if #available(iOS 17.0, *) {
            // iOS 17特定的测试
            XCTAssertTrue(true, "iOS 17功能测试通过")
        }
    }
    
    // MARK: - Device Compatibility Tests
    
    func testDeviceCompatibility() {
        // 测试设备兼容性
        let device = UIDevice.current
        let deviceModel = device.model
        let deviceName = device.name
        
        print("📱 设备信息: \(deviceName) (\(deviceModel))")
        
        // 测试设备类型
        switch device.userInterfaceIdiom {
        case .phone:
            testPhoneCompatibility()
        case .pad:
            testPadCompatibility()
        default:
            print("⚠️ 未知设备类型")
        }
        
        // 测试屏幕尺寸适配
        testScreenSizeCompatibility()
    }
    
    private func testPhoneCompatibility() {
        // 测试iPhone兼容性
        print("📱 测试iPhone兼容性")
        
        let screen = UIScreen.main
        let screenSize = screen.bounds.size
        
        print("📱 屏幕尺寸: \(screenSize.width) x \(screenSize.height)")
        
        // 验证基本功能在iPhone上正常工作
        let coordinate = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
        let location = VirtualLocationModel(name: "iPhone测试", coordinate: coordinate)
        
        dataService.saveLocationToHistory(location)
        let history = dataService.getLocationHistory()
        
        XCTAssertEqual(history.count, 1, "数据服务在iPhone上应该正常工作")
    }
    
    private func testPadCompatibility() {
        // 测试iPad兼容性
        print("📱 测试iPad兼容性")
        
        let screen = UIScreen.main
        let screenSize = screen.bounds.size
        
        print("📱 屏幕尺寸: \(screenSize.width) x \(screenSize.height)")
        
        // 验证基本功能在iPad上正常工作
        let coordinate = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
        let location = VirtualLocationModel(name: "iPad测试", coordinate: coordinate)
        
        dataService.saveLocationToHistory(location)
        let history = dataService.getLocationHistory()
        
        XCTAssertEqual(history.count, 1, "数据服务在iPad上应该正常工作")
    }
    
    private func testScreenSizeCompatibility() {
        // 测试屏幕尺寸兼容性
        let screen = UIScreen.main
        let bounds = screen.bounds
        let scale = screen.scale
        
        print("📱 屏幕信息: \(bounds.width)x\(bounds.height) @\(scale)x")
        
        // 验证屏幕尺寸在支持范围内
        XCTAssertGreaterThan(bounds.width, 320, "屏幕宽度应该大于320点")
        XCTAssertGreaterThan(bounds.height, 480, "屏幕高度应该大于480点")
        
        // 测试不同屏幕密度
        XCTAssertGreaterThanOrEqual(scale, 1.0, "屏幕密度应该大于等于1.0")
        XCTAssertLessThanOrEqual(scale, 4.0, "屏幕密度应该小于等于4.0")
    }
    
    // MARK: - Memory Constraints Tests
    
    func testMemoryConstraints() {
        // 测试内存限制兼容性
        let processInfo = ProcessInfo.processInfo
        let physicalMemory = processInfo.physicalMemory
        
        print("💾 物理内存: \(physicalMemory / 1024 / 1024 / 1024)GB")
        
        // 测试在不同内存条件下的表现
        if physicalMemory < 2 * 1024 * 1024 * 1024 { // 小于2GB
            testLowMemoryConditions()
        } else if physicalMemory < 4 * 1024 * 1024 * 1024 { // 小于4GB
            testMediumMemoryConditions()
        } else {
            testHighMemoryConditions()
        }
    }
    
    private func testLowMemoryConditions() {
        // 测试低内存条件
        print("💾 测试低内存条件")
        
        // 在低内存条件下，限制数据量
        let limitedLocations = (0..<100).map { i in
            VirtualLocationModel(
                name: "低内存测试\(i)",
                coordinate: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
            )
        }
        
        for location in limitedLocations {
            dataService.saveLocationToHistory(location)
        }
        
        let history = dataService.getLocationHistory()
        XCTAssertLessThanOrEqual(history.count, 100, "低内存条件下应该限制数据量")
        
        dataService.clearLocationHistory()
    }
    
    private func testMediumMemoryConditions() {
        // 测试中等内存条件
        print("💾 测试中等内存条件")
        
        let mediumLocations = (0..<500).map { i in
            VirtualLocationModel(
                name: "中等内存测试\(i)",
                coordinate: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
            )
        }
        
        for location in mediumLocations {
            dataService.saveLocationToHistory(location)
        }
        
        let history = dataService.getLocationHistory()
        XCTAssertGreaterThan(history.count, 0, "中等内存条件下应该能处理适量数据")
        
        dataService.clearLocationHistory()
    }
    
    private func testHighMemoryConditions() {
        // 测试高内存条件
        print("💾 测试高内存条件")
        
        let largeLocations = (0..<1000).map { i in
            VirtualLocationModel(
                name: "高内存测试\(i)",
                coordinate: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
            )
        }
        
        for location in largeLocations {
            dataService.saveLocationToHistory(location)
        }
        
        let history = dataService.getLocationHistory()
        XCTAssertGreaterThan(history.count, 0, "高内存条件下应该能处理大量数据")
        
        dataService.clearLocationHistory()
    }
    
    // MARK: - Network Environment Tests
    
    func testNetworkEnvironments() {
        // 测试网络环境兼容性
        print("🌐 测试网络环境兼容性")
        
        // 测试无网络环境
        testOfflineMode()
        
        // 测试弱网络环境
        testWeakNetworkConditions()
        
        // 测试正常网络环境
        testNormalNetworkConditions()
    }
    
    private func testOfflineMode() {
        // 测试离线模式
        print("🌐 测试离线模式")
        
        // 验证核心功能在离线状态下仍然工作
        let coordinate = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
        locationService.startVirtualLocation(at: coordinate)
        
        // 验证虚拟定位在离线状态下正常工作
        XCTAssertNotNil(locationService.currentLocation, "离线模式下虚拟定位应该正常工作")
        
        locationService.stopVirtualLocation()
    }
    
    private func testWeakNetworkConditions() {
        // 测试弱网络条件
        print("🌐 测试弱网络条件")
        
        // 模拟网络延迟对地址解析的影响
        let expectation = XCTestExpectation(description: "弱网络地址解析")
        
        let geocoder = CLGeocoder()
        let location = CLLocation(latitude: 39.9042, longitude: 116.4074)
        
        geocoder.reverseGeocodeLocation(location) { placemarks, error in
            // 在弱网络条件下，可能会超时或失败
            if let error = error {
                print("🌐 弱网络条件下地址解析失败: \(error.localizedDescription)")
            } else {
                print("🌐 弱网络条件下地址解析成功")
            }
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 10.0) // 增加超时时间以适应弱网络
    }
    
    private func testNormalNetworkConditions() {
        // 测试正常网络条件
        print("🌐 测试正常网络条件")
        
        // 在正常网络条件下，所有功能应该正常工作
        let coordinate = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
        let location = VirtualLocationModel(name: "网络测试", coordinate: coordinate)
        
        dataService.saveLocationToHistory(location)
        let history = dataService.getLocationHistory()
        
        XCTAssertEqual(history.count, 1, "正常网络条件下数据服务应该正常工作")
        
        dataService.clearLocationHistory()
    }
    
    // MARK: - Processor Architecture Tests
    
    func testProcessorArchitecture() {
        // 测试处理器架构兼容性
        let processInfo = ProcessInfo.processInfo
        
        print("🔧 处理器信息:")
        print("🔧 处理器数量: \(processInfo.processorCount)")
        print("🔧 活跃处理器数量: \(processInfo.activeProcessorCount)")
        
        // 测试多核处理器支持
        XCTAssertGreaterThan(processInfo.processorCount, 0, "应该检测到处理器")
        XCTAssertGreaterThan(processInfo.activeProcessorCount, 0, "应该有活跃的处理器")
        
        // 测试并发操作
        testConcurrentOperations()
    }
    
    private func testConcurrentOperations() {
        // 测试并发操作
        print("🔧 测试并发操作")
        
        let expectation = XCTestExpectation(description: "并发操作")
        expectation.expectedFulfillmentCount = 5
        
        let queue = DispatchQueue.global(qos: .userInitiated)
        
        for i in 0..<5 {
            queue.async {
                // 并发执行位置操作
                let coordinate = CLLocationCoordinate2D(
                    latitude: 39.9042 + Double(i) * 0.001,
                    longitude: 116.4074 + Double(i) * 0.001
                )
                let location = VirtualLocationModel(name: "并发测试\(i)", coordinate: coordinate)
                
                self.dataService.saveLocationToHistory(location)
                expectation.fulfill()
            }
        }
        
        wait(for: [expectation], timeout: 5.0)
        
        // 验证并发操作结果
        let history = dataService.getLocationHistory()
        XCTAssertEqual(history.count, 5, "并发操作应该成功保存5个位置")
        
        dataService.clearLocationHistory()
    }
    
    // MARK: - Storage Compatibility Tests
    
    func testStorageCompatibility() {
        // 测试存储兼容性
        print("💾 测试存储兼容性")
        
        // 测试UserDefaults存储
        testUserDefaultsStorage()
        
        // 测试文件系统存储
        testFileSystemStorage()
        
        // 测试存储空间限制
        testStorageSpaceLimits()
    }
    
    private func testUserDefaultsStorage() {
        // 测试UserDefaults存储
        let testData = ["key1": "value1", "key2": "value2"]
        let testKey = "CompatibilityTest"
        
        UserDefaults.standard.set(testData, forKey: testKey)
        
        let retrievedData = UserDefaults.standard.dictionary(forKey: testKey) as? [String: String]
        XCTAssertEqual(retrievedData, testData, "UserDefaults存储应该正常工作")
        
        UserDefaults.standard.removeObject(forKey: testKey)
    }
    
    private func testFileSystemStorage() {
        // 测试文件系统存储
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let testFileURL = documentsPath.appendingPathComponent("compatibility_test.txt")
        
        let testContent = "兼容性测试内容"
        
        do {
            try testContent.write(to: testFileURL, atomically: true, encoding: .utf8)
            
            let retrievedContent = try String(contentsOf: testFileURL, encoding: .utf8)
            XCTAssertEqual(retrievedContent, testContent, "文件系统存储应该正常工作")
            
            try FileManager.default.removeItem(at: testFileURL)
        } catch {
            XCTFail("文件系统操作失败: \(error)")
        }
    }
    
    private func testStorageSpaceLimits() {
        // 测试存储空间限制
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        
        do {
            let resourceValues = try documentsPath.resourceValues(forKeys: [.volumeAvailableCapacityKey])
            if let availableCapacity = resourceValues.volumeAvailableCapacity {
                print("💾 可用存储空间: \(availableCapacity / 1024 / 1024)MB")
                
                // 验证有足够的存储空间
                XCTAssertGreaterThan(availableCapacity, 100 * 1024 * 1024, "应该有至少100MB可用空间")
            }
        } catch {
            print("⚠️ 无法获取存储空间信息: \(error)")
        }
    }
    
    // MARK: - Locale and Language Tests
    
    func testLocaleCompatibility() {
        // 测试本地化兼容性
        let currentLocale = Locale.current
        let languageCode = currentLocale.languageCode
        let regionCode = currentLocale.regionCode
        
        print("🌍 当前语言: \(languageCode ?? "未知")")
        print("🌍 当前地区: \(regionCode ?? "未知")")
        
        // 测试不同语言环境下的功能
        testChineseLocale()
        testEnglishLocale()
        testNumberFormatting()
        testDateFormatting()
    }
    
    private func testChineseLocale() {
        // 测试中文环境
        let chineseLocale = Locale(identifier: "zh_CN")
        
        let formatter = NumberFormatter()
        formatter.locale = chineseLocale
        formatter.numberStyle = .decimal
        
        let number = 1234.56
        let formattedNumber = formatter.string(from: NSNumber(value: number))
        
        XCTAssertNotNil(formattedNumber, "中文环境下数字格式化应该正常工作")
        print("🌍 中文数字格式: \(formattedNumber ?? "nil")")
    }
    
    private func testEnglishLocale() {
        // 测试英文环境
        let englishLocale = Locale(identifier: "en_US")
        
        let formatter = NumberFormatter()
        formatter.locale = englishLocale
        formatter.numberStyle = .decimal
        
        let number = 1234.56
        let formattedNumber = formatter.string(from: NSNumber(value: number))
        
        XCTAssertNotNil(formattedNumber, "英文环境下数字格式化应该正常工作")
        print("🌍 英文数字格式: \(formattedNumber ?? "nil")")
    }
    
    private func testNumberFormatting() {
        // 测试数字格式化
        let coordinate = CLLocationCoordinate2D(latitude: 39.904200, longitude: 116.407400)
        let formattedString = coordinate.formattedString
        
        XCTAssertTrue(formattedString.contains("39.904200"), "坐标格式化应该包含正确的纬度")
        XCTAssertTrue(formattedString.contains("116.407400"), "坐标格式化应该包含正确的经度")
    }
    
    private func testDateFormatting() {
        // 测试日期格式化
        let date = Date()
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .medium
        
        let formattedDate = formatter.string(from: date)
        XCTAssertFalse(formattedDate.isEmpty, "日期格式化应该返回非空字符串")
        
        print("🌍 格式化日期: \(formattedDate)")
    }
}
