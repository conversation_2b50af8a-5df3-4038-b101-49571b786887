//
//  MainViewController.swift
//  VirtualLocationApp
//
//  Created by <PERSON><PERSON><PERSON> on 2025-06-20.
//  Copyright © 2025 YourCompany. All rights reserved.
//

import UIKit
import MapKit
import CoreLocation

class MainViewController: UIViewController {
    
    // MARK: - IBOutlets
    
    @IBOutlet weak var mapView: MKMapView!
    @IBOutlet weak var controlPanelView: UIView!
    @IBOutlet weak var startButton: UIButton!
    @IBOutlet weak var stopButton: UIButton!
    @IBOutlet weak var coordinateLabel: UILabel!
    @IBOutlet weak var accuracySlider: UISlider!
    @IBOutlet weak var accuracyLabel: UILabel!
    @IBOutlet weak var statusLabel: UILabel!
    @IBOutlet weak var searchButton: UIButton!
    @IBOutlet weak var currentLocationButton: UIButton!
    @IBOutlet weak var mapTypeSegmentedControl: UISegmentedControl!
    
    // MARK: - Properties
    
    private let locationService = LocationService.shared
    private let dataService = DataService.shared
    private let errorHandler = ErrorHandlingService.shared
    private var currentVirtualLocation: VirtualLocationModel?
    private var isVirtualLocationActive = false
    private var searchController: UISearchController?
    private var geocoder = CLGeocoder()
    private var currentAnnotation: MKPointAnnotation?
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupMapView()
        setupLocationService()
        setupGestureRecognizers()
        updateUI()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        requestLocationPermissionIfNeeded()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        title = "虚拟定位"
        view.backgroundColor = UIColor.systemBackground
        
        // 设置导航栏按钮
        setupNavigationBarButtons()

        // 设置控制面板
        setupControlPanel()

        // 设置精度滑块
        setupAccuracySlider()

        // 设置搜索功能
        setupSearchController()

        // 设置地图类型控制
        setupMapTypeControl()

        // 设置快捷按钮
        setupQuickActionButtons()
    }
    
    private func setupNavigationBarButtons() {
        // 设置按钮
        let settingsButton = UIBarButtonItem(
            image: UIImage(systemName: "gearshape"),
            style: .plain,
            target: self,
            action: #selector(settingsButtonTapped)
        )
        
        // 历史记录按钮
        let historyButton = UIBarButtonItem(
            image: UIImage(systemName: "clock"),
            style: .plain,
            target: self,
            action: #selector(historyButtonTapped)
        )
        
        navigationItem.rightBarButtonItems = [settingsButton, historyButton]
    }
    
    private func setupControlPanel() {
        controlPanelView.backgroundColor = UIColor.systemBackground
        controlPanelView.layer.cornerRadius = 12
        controlPanelView.layer.shadowColor = UIColor.black.cgColor
        controlPanelView.layer.shadowOffset = CGSize(width: 0, height: -2)
        controlPanelView.layer.shadowOpacity = 0.1
        controlPanelView.layer.shadowRadius = 4
        
        // 设置按钮样式
        setupButtons()
        
        // 设置标签
        setupLabels()
    }
    
    private func setupButtons() {
        // 开始按钮
        startButton.setTitle("开始虚拟定位", for: .normal)
        startButton.backgroundColor = UIColor.systemGreen
        startButton.setTitleColor(.white, for: .normal)
        startButton.layer.cornerRadius = 8
        startButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        
        // 停止按钮
        stopButton.setTitle("停止虚拟定位", for: .normal)
        stopButton.backgroundColor = UIColor.systemRed
        stopButton.setTitleColor(.white, for: .normal)
        stopButton.layer.cornerRadius = 8
        stopButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        stopButton.isEnabled = false
        stopButton.alpha = 0.6
    }
    
    private func setupLabels() {
        coordinateLabel.text = "点击地图选择位置"
        coordinateLabel.font = UIFont.systemFont(ofSize: 14)
        coordinateLabel.textColor = UIColor.secondaryLabel
        coordinateLabel.numberOfLines = 2
        
        statusLabel.text = "虚拟定位未激活"
        statusLabel.font = UIFont.systemFont(ofSize: 12)
        statusLabel.textColor = UIColor.secondaryLabel
        
        accuracyLabel.text = "精度: 5m"
        accuracyLabel.font = UIFont.systemFont(ofSize: 12)
        accuracyLabel.textColor = UIColor.secondaryLabel
    }
    
    private func setupAccuracySlider() {
        accuracySlider.minimumValue = 1.0
        accuracySlider.maximumValue = 100.0
        accuracySlider.value = 5.0
        accuracySlider.addTarget(self, action: #selector(accuracySliderChanged(_:)), for: .valueChanged)
    }

    private func setupSearchController() {
        searchController = UISearchController(searchResultsController: nil)
        searchController?.searchResultsUpdater = self
        searchController?.obscuresBackgroundDuringPresentation = false
        searchController?.searchBar.placeholder = "搜索地址或地点"
        searchController?.searchBar.delegate = self

        // 将搜索控制器添加到导航栏
        navigationItem.searchController = searchController
        definesPresentationContext = true
    }

    private func setupMapTypeControl() {
        mapTypeSegmentedControl.removeAllSegments()
        mapTypeSegmentedControl.insertSegment(withTitle: "标准", at: 0, animated: false)
        mapTypeSegmentedControl.insertSegment(withTitle: "卫星", at: 1, animated: false)
        mapTypeSegmentedControl.insertSegment(withTitle: "混合", at: 2, animated: false)
        mapTypeSegmentedControl.selectedSegmentIndex = 0
        mapTypeSegmentedControl.addTarget(self, action: #selector(mapTypeChanged(_:)), for: .valueChanged)
    }

    private func setupQuickActionButtons() {
        // 搜索按钮
        searchButton.setImage(UIImage(systemName: "magnifyingglass"), for: .normal)
        searchButton.backgroundColor = UIColor.systemBlue
        searchButton.tintColor = .white
        searchButton.layer.cornerRadius = 25
        searchButton.addTarget(self, action: #selector(searchButtonTapped), for: .touchUpInside)

        // 当前位置按钮
        currentLocationButton.setImage(UIImage(systemName: "location.fill"), for: .normal)
        currentLocationButton.backgroundColor = UIColor.systemBlue
        currentLocationButton.tintColor = .white
        currentLocationButton.layer.cornerRadius = 25
        currentLocationButton.addTarget(self, action: #selector(currentLocationButtonTapped), for: .touchUpInside)
    }
    
    private func setupMapView() {
        mapView.delegate = self
        mapView.showsUserLocation = true
        mapView.userTrackingMode = .none
        mapView.mapType = .standard
        
        // 设置默认区域（北京）
        let defaultCoordinate = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
        let region = MKCoordinateRegion(center: defaultCoordinate, latitudinalMeters: 10000, longitudinalMeters: 10000)
        mapView.setRegion(region, animated: false)
    }
    
    private func setupLocationService() {
        locationService.delegate = self
    }
    
    private func setupGestureRecognizers() {
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(mapTapped(_:)))
        mapView.addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Actions
    
    @IBAction func startButtonTapped(_ sender: UIButton) {
        guard let virtualLocation = currentVirtualLocation else {
            AnimationHelper.animateButtonError(sender)
            HapticFeedbackHelper.error()
            showAlert(title: "提示", message: "请先在地图上选择一个位置")
            return
        }

        AnimationHelper.animateButtonTap(sender) {
            self.startVirtualLocation(virtualLocation)
        }
        HapticFeedbackHelper.mediumImpact()
    }
    
    @IBAction func stopButtonTapped(_ sender: UIButton) {
        AnimationHelper.animateButtonTap(sender) {
            self.stopVirtualLocation()
        }
        HapticFeedbackHelper.mediumImpact()
    }
    
    @objc private func accuracySliderChanged(_ slider: UISlider) {
        let accuracy = Double(slider.value)
        accuracyLabel.text = String(format: "精度: %.0fm", accuracy)
        HapticFeedbackHelper.lightImpact()
    }

    @objc private func mapTypeChanged(_ segmentedControl: UISegmentedControl) {
        HapticFeedbackHelper.selection()

        switch segmentedControl.selectedSegmentIndex {
        case 0:
            mapView.mapType = .standard
        case 1:
            mapView.mapType = .satellite
        case 2:
            mapView.mapType = .hybrid
        default:
            mapView.mapType = .standard
        }
    }

    @objc private func searchButtonTapped() {
        searchController?.searchBar.becomeFirstResponder()
    }

    @objc private func currentLocationButtonTapped() {
        guard let currentLocation = locationService.currentLocation else {
            showAlert(title: "提示", message: "无法获取当前位置")
            return
        }

        let region = MKCoordinateRegion(center: currentLocation.coordinate, latitudinalMeters: 1000, longitudinalMeters: 1000)
        mapView.setRegion(region, animated: true)
    }
    
    @objc private func mapTapped(_ gesture: UITapGestureRecognizer) {
        let point = gesture.location(in: mapView)
        let coordinate = mapView.convert(point, toCoordinateFrom: mapView)
        selectLocation(at: coordinate)
    }
    
    @objc private func settingsButtonTapped() {
        let settingsVC = SettingsViewController()
        let navigationController = UINavigationController(rootViewController: settingsVC)
        navigationController.modalPresentationStyle = .pageSheet

        if let sheet = navigationController.sheetPresentationController {
            sheet.detents = [.medium(), .large()]
            sheet.prefersGrabberVisible = true
        }

        present(navigationController, animated: true)
    }
    
    @objc private func historyButtonTapped() {
        let historyVC = HistoryViewController()
        historyVC.delegate = self
        let navigationController = UINavigationController(rootViewController: historyVC)
        navigationController.modalPresentationStyle = .pageSheet

        if let sheet = navigationController.sheetPresentationController {
            sheet.detents = [.medium(), .large()]
            sheet.prefersGrabberVisible = true
        }

        present(navigationController, animated: true)
    }
    
    // MARK: - Private Methods
    
    private func requestLocationPermissionIfNeeded() {
        if locationService.authorizationStatus == .notDetermined {
            locationService.requestLocationPermission()
        }
    }
    
    private func selectLocation(at coordinate: CLLocationCoordinate2D) {
        // 移除之前的标注
        if let currentAnnotation = currentAnnotation {
            mapView.removeAnnotation(currentAnnotation)
        }

        // 创建新的标注
        currentAnnotation = MKPointAnnotation()
        currentAnnotation?.coordinate = coordinate
        currentAnnotation?.title = "虚拟位置"
        currentAnnotation?.subtitle = String(format: "%.6f, %.6f", coordinate.latitude, coordinate.longitude)

        if let annotation = currentAnnotation {
            mapView.addAnnotation(annotation)
        }

        // 开始地址解析
        reverseGeocodeLocation(coordinate: coordinate)

        // 更新坐标标签
        coordinateLabel.text = String(format: "纬度: %.6f\n经度: %.6f", coordinate.latitude, coordinate.longitude)

        // 启用开始按钮并添加动画
        UIView.animate(withDuration: 0.3) {
            self.startButton.isEnabled = true
            self.startButton.alpha = 1.0
        }

        // 添加触觉反馈
        HapticFeedbackHelper.lightImpact()

        print("✅ 位置已选择: \(coordinate.latitude), \(coordinate.longitude)")
    }

    private func reverseGeocodeLocation(coordinate: CLLocationCoordinate2D) {
        let location = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)

        geocoder.reverseGeocodeLocation(location) { [weak self] placemarks, error in
            DispatchQueue.main.async {
                guard let self = self else { return }

                if let error = error {
                    print("地址解析失败: \(error.localizedDescription)")
                    self.createVirtualLocation(coordinate: coordinate, address: nil)
                    return
                }

                if let placemark = placemarks?.first {
                    let address = self.formatAddress(from: placemark)
                    self.currentAnnotation?.subtitle = address
                    self.createVirtualLocation(coordinate: coordinate, address: address)
                } else {
                    self.createVirtualLocation(coordinate: coordinate, address: nil)
                }
            }
        }
    }

    private func createVirtualLocation(coordinate: CLLocationCoordinate2D, address: String?) {
        let accuracy = Double(accuracySlider.value)
        currentVirtualLocation = VirtualLocationModel(
            name: address ?? "选中位置",
            coordinate: coordinate,
            accuracy: accuracy,
            address: address
        )
    }

    private func formatAddress(from placemark: CLPlacemark) -> String {
        var addressComponents: [String] = []

        if let country = placemark.country {
            addressComponents.append(country)
        }
        if let administrativeArea = placemark.administrativeArea {
            addressComponents.append(administrativeArea)
        }
        if let locality = placemark.locality {
            addressComponents.append(locality)
        }
        if let thoroughfare = placemark.thoroughfare {
            addressComponents.append(thoroughfare)
        }
        if let subThoroughfare = placemark.subThoroughfare {
            addressComponents.append(subThoroughfare)
        }

        return addressComponents.joined(separator: " ")
    }
    
    private func startVirtualLocation(_ location: VirtualLocationModel) {
        locationService.startVirtualLocation(at: location.coordinate, accuracy: location.accuracy)

        isVirtualLocationActive = true
        updateUI()

        // 保存到历史记录
        dataService.saveLocationToHistory(location)
        dataService.saveLastUsedLocation(location)

        // 添加成功动画和反馈
        AnimationHelper.animateButtonSuccess(startButton)
        HapticFeedbackHelper.success()

        // 显示Toast提示
        ToastHelper.showToast(message: "虚拟定位已启动", in: view)
    }
    
    private func stopVirtualLocation() {
        locationService.stopVirtualLocation()

        isVirtualLocationActive = false
        updateUI()

        // 添加成功反馈
        HapticFeedbackHelper.success()

        // 显示Toast提示
        ToastHelper.showToast(message: "虚拟定位已停止", in: view)
    }
    
    private func updateUI() {
        DispatchQueue.main.async {
            if self.isVirtualLocationActive {
                self.startButton.isEnabled = false
                self.startButton.alpha = 0.6
                self.stopButton.isEnabled = true
                self.stopButton.alpha = 1.0
                self.statusLabel.text = "虚拟定位已激活"
                self.statusLabel.textColor = UIColor.systemGreen
            } else {
                self.startButton.isEnabled = self.currentVirtualLocation != nil
                self.startButton.alpha = self.currentVirtualLocation != nil ? 1.0 : 0.6
                self.stopButton.isEnabled = false
                self.stopButton.alpha = 0.6
                self.statusLabel.text = "虚拟定位未激活"
                self.statusLabel.textColor = UIColor.secondaryLabel
            }
        }
    }
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    private func showSuccessMessage(_ message: String) {
        let alert = UIAlertController(title: "成功", message: message, preferredStyle: .alert)
        present(alert, animated: true)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            alert.dismiss(animated: true)
        }
    }
}

// MARK: - MKMapViewDelegate

extension MainViewController: MKMapViewDelegate {
    
    func mapView(_ mapView: MKMapView, viewFor annotation: MKAnnotation) -> MKAnnotationView? {
        guard !(annotation is MKUserLocation) else { return nil }
        
        let identifier = "VirtualLocationPin"
        var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier)
        
        if annotationView == nil {
            annotationView = MKPinAnnotationView(annotation: annotation, reuseIdentifier: identifier)
            annotationView?.canShowCallout = true
        } else {
            annotationView?.annotation = annotation
        }
        
        if let pinView = annotationView as? MKPinAnnotationView {
            pinView.pinTintColor = UIColor.systemBlue
        }
        
        return annotationView
    }
}

// MARK: - LocationServiceDelegate

extension MainViewController: LocationServiceDelegate {
    
    func locationService(_ service: LocationService, didUpdateLocation location: CLLocation) {
        DispatchQueue.main.async {
            // 更新地图中心（仅在非虚拟定位模式下）
            if !self.isVirtualLocationActive {
                let region = MKCoordinateRegion(center: location.coordinate, latitudinalMeters: 1000, longitudinalMeters: 1000)
                self.mapView.setRegion(region, animated: true)
            }
        }
    }
    
    func locationService(_ service: LocationService, didFailWithError error: Error) {
        DispatchQueue.main.async {
            // 使用错误处理服务处理错误
            if let locationError = error as? LocationError {
                self.errorHandler.handleLocationError(locationError, showToUser: true)
            } else {
                self.errorHandler.handleError(error, context: "LocationService", showToUser: true)
            }
        }
    }
    
    func locationService(_ service: LocationService, didChangeAuthorizationStatus status: CLAuthorizationStatus) {
        DispatchQueue.main.async {
            switch status {
            case .denied, .restricted:
                self.showAlert(title: "位置权限", message: "请在设置中开启位置权限以使用此功能")
            case .authorizedWhenInUse, .authorizedAlways:
                print("✅ 位置权限已获取")
            default:
                break
            }
        }
    }
}

// MARK: - UISearchResultsUpdating

extension MainViewController: UISearchResultsUpdating {

    func updateSearchResults(for searchController: UISearchController) {
        // 搜索结果更新逻辑可以在这里实现
        // 当前版本使用searchBarSearchButtonClicked处理搜索
    }
}

// MARK: - UISearchBarDelegate

extension MainViewController: UISearchBarDelegate {

    func searchBarSearchButtonClicked(_ searchBar: UISearchBar) {
        guard let searchText = searchBar.text, !searchText.isEmpty else { return }

        searchBar.resignFirstResponder()
        searchLocation(query: searchText)
    }

    private func searchLocation(query: String) {
        geocoder.geocodeAddressString(query) { [weak self] placemarks, error in
            DispatchQueue.main.async {
                guard let self = self else { return }

                if let error = error {
                    self.showAlert(title: "搜索失败", message: "无法找到该地址: \(error.localizedDescription)")
                    return
                }

                guard let placemark = placemarks?.first,
                      let location = placemark.location else {
                    self.showAlert(title: "搜索失败", message: "未找到匹配的地址")
                    return
                }

                // 移动地图到搜索结果
                let coordinate = location.coordinate
                let region = MKCoordinateRegion(center: coordinate, latitudinalMeters: 1000, longitudinalMeters: 1000)
                self.mapView.setRegion(region, animated: true)

                // 自动选择该位置
                self.selectLocation(at: coordinate)

                // 显示成功提示
                let address = self.formatAddress(from: placemark)
                self.showSuccessMessage("已找到: \(address)")
            }
        }
    }
}

// MARK: - HistoryViewControllerDelegate

extension MainViewController: HistoryViewControllerDelegate {

    func historyViewController(_ controller: HistoryViewController, didSelectLocation location: VirtualLocationModel) {
        // 在地图上显示选中的位置
        let region = MKCoordinateRegion(center: location.coordinate, latitudinalMeters: 1000, longitudinalMeters: 1000)
        mapView.setRegion(region, animated: true)

        // 自动选择该位置
        selectLocation(at: location.coordinate)

        // 显示成功提示
        showSuccessMessage("已选择位置: \(location.name)")
    }
}
