<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="location.circle.fill" catalog="system" translatesAutoresizingMaskIntoConstraints="NO" id="app-icon-imageview">
                                <rect key="frame" x="146.66666666666666" y="356" width="100" height="98.666666666666686"/>
                                <color key="tintColor" systemColor="systemBlueColor"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="100" id="icon-width"/>
                                    <constraint firstAttribute="height" constant="100" id="icon-height"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="虚拟定位助手" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="app-title-label">
                                <rect key="frame" x="50" y="474.66666666666669" width="293" height="28.666666666666686"/>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="24"/>
                                <color key="textColor" systemColor="labelColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="专业的GPS位置模拟工具" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="app-subtitle-label">
                                <rect key="frame" x="50" y="519.33333333333337" width="293" height="19.333333333333371"/>
                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                <color key="textColor" systemColor="secondaryLabelColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="v1.0" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="version-label">
                                <rect key="frame" x="50" y="774" width="293" height="14.333333333333371"/>
                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                <color key="textColor" systemColor="tertiaryLabelColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <activityIndicatorView opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" animating="YES" style="medium" translatesAutoresizingMaskIntoConstraints="NO" id="loading-indicator">
                                <rect key="frame" x="186.66666666666666" y="578.66666666666663" width="20" height="20"/>
                                <color key="color" systemColor="systemBlueColor"/>
                            </activityIndicatorView>
                        </subviews>
                        <viewLayoutMarginsGuide key="safeArea" id="6Tk-OE-BBY"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <!-- App Icon Constraints -->
                            <constraint firstItem="app-icon-imageview" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="icon-center-x"/>
                            <constraint firstItem="app-icon-imageview" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" constant="-20" id="icon-center-y"/>
                            
                            <!-- App Title Constraints -->
                            <constraint firstItem="app-title-label" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="50" id="title-leading"/>
                            <constraint firstItem="app-title-label" firstAttribute="trailing" secondItem="6Tk-OE-BBY" secondAttribute="trailing" constant="-50" id="title-trailing"/>
                            <constraint firstItem="app-title-label" firstAttribute="top" secondItem="app-icon-imageview" secondAttribute="bottom" constant="20" id="title-top"/>
                            
                            <!-- App Subtitle Constraints -->
                            <constraint firstItem="app-subtitle-label" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="50" id="subtitle-leading"/>
                            <constraint firstItem="app-subtitle-label" firstAttribute="trailing" secondItem="6Tk-OE-BBY" secondAttribute="trailing" constant="-50" id="subtitle-trailing"/>
                            <constraint firstItem="app-subtitle-label" firstAttribute="top" secondItem="app-title-label" secondAttribute="bottom" constant="16" id="subtitle-top"/>
                            
                            <!-- Loading Indicator Constraints -->
                            <constraint firstItem="loading-indicator" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="loading-center-x"/>
                            <constraint firstItem="loading-indicator" firstAttribute="top" secondItem="app-subtitle-label" secondAttribute="bottom" constant="40" id="loading-top"/>
                            
                            <!-- Version Label Constraints -->
                            <constraint firstItem="version-label" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="50" id="version-leading"/>
                            <constraint firstItem="version-label" firstAttribute="trailing" secondItem="6Tk-OE-BBY" secondAttribute="trailing" constant="-50" id="version-trailing"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="bottom" secondItem="version-label" secondAttribute="bottom" constant="30" id="version-bottom"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="53" y="375"/>
        </scene>
    </scenes>
    <resources>
        <image name="location.circle.fill" catalog="system" width="128" height="123"/>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="secondaryLabelColor">
            <color red="0.23529411764705882" green="0.23529411764705882" blue="0.2627450980392157" alpha="0.59999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBlueColor">
            <color red="0.0" green="0.47843137254901963" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="tertiaryLabelColor">
            <color red="0.23529411764705882" green="0.23529411764705882" blue="0.2627450980392157" alpha="0.29999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
