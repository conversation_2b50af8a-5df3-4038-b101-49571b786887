//
//  VirtualLocationAppTests.swift
//  VirtualLocationAppTests
//
//  Created by <PERSON><PERSON><PERSON> on 2025-06-20.
//  Copyright © 2025 YourCompany. All rights reserved.
//

import XCTest
import CoreLocation
@testable import VirtualLocationApp

class VirtualLocationAppTests: XCTestCase {
    
    // MARK: - Test Properties
    
    var locationService: LocationService!
    var dataService: DataService!
    var pathSimulationService: PathSimulationService!
    var errorHandlingService: ErrorHandlingService!
    
    // MARK: - Setup and Teardown
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        
        // 初始化测试对象
        locationService = LocationService.shared
        dataService = DataService.shared
        pathSimulationService = PathSimulationService()
        errorHandlingService = ErrorHandlingService.shared
        
        // 清理测试数据
        dataService.clearAllData()
        errorHandlingService.clearErrorLog()
    }
    
    override func tearDownWithError() throws {
        // 清理测试数据
        dataService.clearAllData()
        errorHandlingService.clearErrorLog()
        
        locationService = nil
        dataService = nil
        pathSimulationService = nil
        errorHandlingService = nil
        
        try super.tearDownWithError()
    }
    
    // MARK: - LocationService Tests
    
    func testLocationServiceSingleton() {
        // 测试单例模式
        let instance1 = LocationService.shared
        let instance2 = LocationService.shared
        
        XCTAssertTrue(instance1 === instance2, "LocationService应该是单例")
    }
    
    func testLocationServiceProperties() {
        // 测试基本属性
        XCTAssertNotNil(locationService.authorizationStatus, "授权状态不应为空")
        XCTAssertNotNil(locationService.isLocationServicesEnabled, "位置服务状态不应为空")
    }
    
    func testVirtualLocationValidation() {
        // 测试有效坐标
        let validCoordinate = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
        XCTAssertTrue(CLLocationCoordinate2DIsValid(validCoordinate), "北京坐标应该有效")
        
        // 测试无效坐标
        let invalidCoordinate = CLLocationCoordinate2D(latitude: 200, longitude: 200)
        XCTAssertFalse(CLLocationCoordinate2DIsValid(invalidCoordinate), "超出范围的坐标应该无效")
    }
    
    func testLocationUpdateInterval() {
        // 测试更新间隔设置
        locationService.setLocationUpdateInterval(2.0)
        
        // 测试边界值
        locationService.setLocationUpdateInterval(0.05) // 应该被限制为0.1
        locationService.setLocationUpdateInterval(10.0) // 应该被限制为5.0
        
        XCTAssertTrue(true, "更新间隔设置应该成功")
    }
    
    // MARK: - DataService Tests
    
    func testDataServiceSingleton() {
        // 测试单例模式
        let instance1 = DataService.shared
        let instance2 = DataService.shared
        
        XCTAssertTrue(instance1 === instance2, "DataService应该是单例")
    }
    
    func testVirtualLocationModelCreation() {
        // 测试虚拟位置模型创建
        let coordinate = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
        let location = VirtualLocationModel(
            name: "测试位置",
            coordinate: coordinate,
            accuracy: 5.0,
            address: "北京市"
        )
        
        XCTAssertEqual(location.name, "测试位置")
        XCTAssertEqual(location.latitude, 39.9042, accuracy: 0.0001)
        XCTAssertEqual(location.longitude, 116.4074, accuracy: 0.0001)
        XCTAssertEqual(location.accuracy, 5.0)
        XCTAssertEqual(location.address, "北京市")
    }
    
    func testLocationHistoryManagement() {
        // 测试位置历史记录管理
        let coordinate = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
        let location = VirtualLocationModel(name: "测试位置", coordinate: coordinate)
        
        // 保存位置
        dataService.saveLocationToHistory(location)
        
        // 获取历史记录
        let history = dataService.getLocationHistory()
        XCTAssertEqual(history.count, 1, "应该有一条历史记录")
        XCTAssertEqual(history.first?.name, "测试位置")
        
        // 删除位置
        dataService.deleteLocationFromHistory(withId: location.id)
        let emptyHistory = dataService.getLocationHistory()
        XCTAssertEqual(emptyHistory.count, 0, "历史记录应该为空")
    }
    
    func testFavoriteLocationsManagement() {
        // 测试收藏位置管理
        let coordinate = CLLocationCoordinate2D(latitude: 31.2304, longitude: 121.4737)
        let location = VirtualLocationModel(name: "上海外滩", coordinate: coordinate)
        
        // 保存收藏
        dataService.saveFavoriteLocation(location)
        
        // 检查是否收藏
        XCTAssertTrue(dataService.isLocationFavorited(withId: location.id), "位置应该被收藏")
        
        // 获取收藏列表
        let favorites = dataService.getFavoriteLocations()
        XCTAssertEqual(favorites.count, 1, "应该有一个收藏位置")
        
        // 删除收藏
        dataService.deleteFavoriteLocation(withId: location.id)
        XCTAssertFalse(dataService.isLocationFavorited(withId: location.id), "位置应该不再被收藏")
    }
    
    func testAppConfigurationManagement() {
        // 测试应用配置管理
        var config = dataService.getAppConfiguration()
        
        // 修改配置
        config.defaultAccuracy = 10.0
        config.updateInterval = 2.0
        config.isAutoStartEnabled = true
        
        // 保存配置
        dataService.saveAppConfiguration(config)
        
        // 验证配置
        let savedConfig = dataService.getAppConfiguration()
        XCTAssertEqual(savedConfig.defaultAccuracy, 10.0)
        XCTAssertEqual(savedConfig.updateInterval, 2.0)
        XCTAssertTrue(savedConfig.isAutoStartEnabled)
    }
    
    // MARK: - PathSimulationService Tests
    
    func testPathModelCreation() {
        // 测试路径模型创建
        let points = [
            CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
            CLLocationCoordinate2D(latitude: 39.9052, longitude: 116.4084),
            CLLocationCoordinate2D(latitude: 39.9062, longitude: 116.4094)
        ]
        
        let path = PathModel(name: "测试路径", points: points, description: "测试用路径")
        
        XCTAssertEqual(path.name, "测试路径")
        XCTAssertEqual(path.points.count, 3)
        XCTAssertGreaterThan(path.totalDistance, 0, "路径总距离应该大于0")
        XCTAssertGreaterThan(path.estimatedDuration, 0, "预估时间应该大于0")
    }
    
    func testPathSimulationInitialization() {
        // 测试路径模拟初始化
        XCTAssertFalse(pathSimulationService.isActive, "初始状态应该不活跃")
        XCTAssertEqual(pathSimulationService.progress, 0.0, "初始进度应该为0")
    }
    
    func testPathSimulationValidation() {
        // 测试空路径验证
        let expectation = XCTestExpectation(description: "空路径应该触发错误")
        
        class TestDelegate: PathSimulationDelegate {
            let expectation: XCTestExpectation
            
            init(expectation: XCTestExpectation) {
                self.expectation = expectation
            }
            
            func pathSimulation(_ service: PathSimulationService, didUpdateLocation location: CLLocation) {}
            
            func pathSimulation(_ service: PathSimulationService, didCompleteWithTotalDistance distance: CLLocationDistance) {}
            
            func pathSimulation(_ service: PathSimulationService, didFailWithError error: Error) {
                if error is PathSimulationError {
                    expectation.fulfill()
                }
            }
        }
        
        pathSimulationService.delegate = TestDelegate(expectation: expectation)
        pathSimulationService.startPathSimulation(path: [], speed: 5.0)
        
        wait(for: [expectation], timeout: 1.0)
    }
    
    // MARK: - ErrorHandlingService Tests
    
    func testErrorHandlingServiceSingleton() {
        // 测试单例模式
        let instance1 = ErrorHandlingService.shared
        let instance2 = ErrorHandlingService.shared
        
        XCTAssertTrue(instance1 === instance2, "ErrorHandlingService应该是单例")
    }
    
    func testErrorLogging() {
        // 测试错误日志记录
        let testError = LocationError.invalidCoordinate
        
        errorHandlingService.handleError(testError, context: "测试", showToUser: false)
        
        let errorLog = errorHandlingService.getErrorLog()
        XCTAssertEqual(errorLog.count, 1, "应该有一条错误日志")
        XCTAssertEqual(errorLog.first?.context, "测试")
    }
    
    func testErrorLogExport() {
        // 测试错误日志导出
        let testError = LocationError.simulationFailed
        errorHandlingService.handleError(testError, context: "导出测试", showToUser: false)
        
        let exportedLog = errorHandlingService.exportErrorLog()
        XCTAssertTrue(exportedLog.contains("虚拟定位应用错误日志"), "导出的日志应该包含标题")
        XCTAssertTrue(exportedLog.contains("导出测试"), "导出的日志应该包含上下文")
    }
    
    func testErrorLogClear() {
        // 测试错误日志清空
        let testError = LocationError.pathSimulationFailed
        errorHandlingService.handleError(testError, showToUser: false)
        
        XCTAssertEqual(errorHandlingService.getErrorLog().count, 1, "应该有一条错误日志")
        
        errorHandlingService.clearErrorLog()
        XCTAssertEqual(errorHandlingService.getErrorLog().count, 0, "错误日志应该被清空")
    }
    
    // MARK: - Coordinate Extensions Tests
    
    func testCoordinateValidation() {
        // 测试坐标验证扩展
        let validCoordinate = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
        let invalidCoordinate = CLLocationCoordinate2D(latitude: 200, longitude: 200)
        
        XCTAssertTrue(validCoordinate.isValid, "有效坐标应该通过验证")
        XCTAssertFalse(invalidCoordinate.isValid, "无效坐标应该不通过验证")
    }
    
    func testCoordinateFormatting() {
        // 测试坐标格式化
        let coordinate = CLLocationCoordinate2D(latitude: 39.904200, longitude: 116.407400)
        
        let formattedString = coordinate.formattedString
        XCTAssertTrue(formattedString.contains("39.904200"), "格式化字符串应该包含纬度")
        XCTAssertTrue(formattedString.contains("116.407400"), "格式化字符串应该包含经度")
    }
    
    func testCoordinateDistance() {
        // 测试坐标距离计算
        let beijing = CLLocationCoordinate2D.beijing
        let shanghai = CLLocationCoordinate2D.shanghai
        
        let distance = beijing.distance(to: shanghai)
        XCTAssertGreaterThan(distance, 1000000, "北京到上海的距离应该大于1000公里")
    }
    
    func testCoordinateBearing() {
        // 测试方位角计算
        let start = CLLocationCoordinate2D(latitude: 0, longitude: 0)
        let north = CLLocationCoordinate2D(latitude: 1, longitude: 0)
        let east = CLLocationCoordinate2D(latitude: 0, longitude: 1)
        
        let bearingToNorth = start.bearing(to: north)
        let bearingToEast = start.bearing(to: east)
        
        XCTAssertEqual(bearingToNorth, 0, accuracy: 1, "正北方向应该是0度")
        XCTAssertEqual(bearingToEast, 90, accuracy: 1, "正东方向应该是90度")
    }
    
    // MARK: - Performance Tests
    
    func testLocationModelPerformance() {
        // 测试位置模型创建性能
        measure {
            for _ in 0..<1000 {
                let coordinate = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
                _ = VirtualLocationModel(name: "性能测试", coordinate: coordinate)
            }
        }
    }
    
    func testDataServicePerformance() {
        // 测试数据服务性能
        let locations = (0..<100).map { i in
            VirtualLocationModel(
                name: "位置\(i)",
                coordinate: CLLocationCoordinate2D(latitude: 39.9042 + Double(i) * 0.001, longitude: 116.4074)
            )
        }
        
        measure {
            for location in locations {
                dataService.saveLocationToHistory(location)
            }
        }
    }
}
